{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/userFavorite.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/UserFavorites`;\r\n\r\nexport async function addToFavorites(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/add`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ propertyId }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"addToFavorites\",\r\n      propertyId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi thêm vào danh sách yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function removeFromFavorites(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/remove/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"removeFromFavorites\",\r\n      propertyId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa khỏi danh sách yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function checkFavoriteStatus(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/check`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ propertyIds: Array.isArray(propertyIds) ? propertyIds : [propertyIds] }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"checkFavoriteStatus\",\r\n      propertyIds,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi kiểm tra trạng thái yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function getFavoritesCount() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/count`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getFavoritesCount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy số lượng bất động sản yêu thích\");\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the user's favorite properties\r\n * @returns {Promise<{success: boolean, data: Array<UserFavoriteDto>, message: string}>} Response with array of UserFavoriteDto objects\r\n * @description UserFavoriteDto contains: id, propertyId, createdAt\r\n */\r\nexport async function getUserFavorites() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/favorites`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserFavorites\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy danh sách bất động sản yêu thích\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;;;;;;AAEA,MAAM,eAAe,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC;AAExD,eAAe,eAAe,UAAU;IAC7C,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,IAAI,CAAC,EAAE;YAChD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAW;QACpC;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,OAAO;YAC7B,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAEO,eAAe,oBAAoB,UAAU;IAClD,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,QAAQ,EAAE,YAAY,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,OAAO;YAC7B,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAEO,eAAe,oBAAoB,WAAW;IACnD,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,MAAM,CAAC,EAAE;YAClD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE,aAAa,MAAM,OAAO,CAAC,eAAe,cAAc;oBAAC;iBAAY;YAAC;QAC/F;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,OAAO;YAC7B,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAEO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,MAAM,CAAC,EAAE;YAClD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,OAAO;YAC7B,QAAQ;QACV;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAOO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,UAAU,CAAC,EAAE;YACtD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,OAAO;YAC7B,QAAQ;QACV;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;;;IAxFsB;IAkBA;IAiBA;IAkBA;IAqBA;;AA1EA,+OAAA;AAkBA,+OAAA;AAiBA,+OAAA;AAkBA,+OAAA;AAqBA,+OAAA", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/notification.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/notifications`;\r\n\r\n/**\r\n * Get notifications with pagination and optional type filtering\r\n * @param {Object} params - Query parameters\r\n * @param {number} params.page - Page number (default: 1)\r\n * @param {number} params.limit - Items per page (default: 10)\r\n * @param {string} params.type - Notification type (news, wallet_update, promotion, customer_message)\r\n * @returns {Promise<Object>} Response with notifications data\r\n */\r\nexport async function getNotifications(params = {}) {\r\n  try {\r\n    const { page = 1, limit = 10, type } = params;\r\n    \r\n    // If type is specified, use the by-type endpoint\r\n    let url = type \r\n      ? `${API_BASE_URL}/by-type/${type}?page=${page}&pageSize=${limit}`\r\n      : `${API_BASE_URL}?page=${page}&pageSize=${limit}`;\r\n    \r\n    return await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getNotifications\",\r\n      params,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông báo\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get unread notification count\r\n * @returns {Promise<Object>} Response with notification count data\r\n */\r\nexport async function getUnreadCount() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/unread-count`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getUnreadCount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy số thông báo chưa đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Mark notifications as read\r\n * @param {Object} params - Request parameters\r\n * @param {Array<string>} params.ids - Array of notification IDs to mark as read\r\n * @returns {Promise<Object>} Response data\r\n */\r\nexport async function markAsRead(params) {\r\n  try {\r\n    // The API expects marking one notification at a time with a specific endpoint\r\n    if (params.ids && params.ids.length > 0) {\r\n      // Mark the first notification in the array\r\n      const id = params.ids[0];\r\n      return await fetchWithAuth(`${API_BASE_URL}/${id}/mark-as-read`, {\r\n        method: \"PUT\", // Changed from POST to PUT as per API doc\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n    }\r\n    return handleErrorResponse(false, null, \"Không có ID thông báo được cung cấp\");\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"markAsRead\",\r\n      params,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi đánh dấu thông báo đã đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Mark all notifications as read\r\n * @returns {Promise<Object>} Response data\r\n */\r\nexport async function markAllAsRead() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/mark-all-as-read`, {\r\n      method: \"PUT\", // Changed from POST to PUT as per API doc\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"markAllAsRead\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi đánh dấu tất cả thông báo đã đọc\");\r\n  }\r\n}\r\n\r\n/**\r\n * Get latest notifications (for navbar dropdown)\r\n * @param {number} limit - Number of notifications to get\r\n * @returns {Promise<Object>} Response with notifications data\r\n */\r\nexport async function getLatestNotifications(limit = 6) {\r\n  try {\r\n    // Using the default endpoint with a small page size for latest notifications\r\n    return await fetchWithAuth(`${API_BASE_URL}?page=1&pageSize=${limit}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"NotificationService\", error, {\r\n      action: \"getLatestNotifications\",\r\n      limit,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy thông báo mới nhất\");\r\n  }\r\n} "], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;;;;;;AAEA,MAAM,eAAe,GAAG,QAAQ,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC;AAUxD,eAAe,iBAAiB,SAAS,CAAC,CAAC;IAChD,IAAI;QACF,MAAM,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,GAAG;QAEvC,iDAAiD;QACjD,IAAI,MAAM,OACN,GAAG,aAAa,SAAS,EAAE,KAAK,MAAM,EAAE,KAAK,UAAU,EAAE,OAAO,GAChE,GAAG,aAAa,MAAM,EAAE,KAAK,UAAU,EAAE,OAAO;QAEpD,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;YAC9B,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB,OAAO;YACrC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAMO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,aAAa,CAAC,EAAE;YACzD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB,OAAO;YACrC,QAAQ;QACV;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAQO,eAAe,WAAW,MAAM;IACrC,IAAI;QACF,8EAA8E;QAC9E,IAAI,OAAO,GAAG,IAAI,OAAO,GAAG,CAAC,MAAM,GAAG,GAAG;YACvC,2CAA2C;YAC3C,MAAM,KAAK,OAAO,GAAG,CAAC,EAAE;YACxB,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB,OAAO;YACrC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAMO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,iBAAiB,CAAC,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB,OAAO;YACrC,QAAQ;QACV;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;AAOO,eAAe,uBAAuB,QAAQ,CAAC;IACpD,IAAI;QACF,6EAA6E;QAC7E,OAAO,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,aAAa,iBAAiB,EAAE,OAAO,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,CAAA,GAAA,+GAAA,CAAA,WAAQ,AAAD,EAAE,uBAAuB,OAAO;YACrC,QAAQ;YACR;QACF;QACA,OAAO,CAAA,GAAA,+GAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,MAAM;IAC1C;AACF;;;IAlHsB;IA4BA;IAsBA;IA2BA;IAqBA;;AAlGA,+OAAA;AA4BA,+OAAA;AAsBA,+OAAA;AA2BA,+OAAA;AAqBA,+OAAA", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/.next-internal/server/app/%5Blocale%5D/%28protected%29/user/bds/new/success/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {validateTokenServer as '001de94ec731220815d4fe6ce2d548b202bd052ff3'} from 'ACTIONS_MODULE0'\nexport {validateTokenDirectlyFromAPIServer as '0034f2076260b358ea3dfc1c99fa419e3287163fe8'} from 'ACTIONS_MODULE0'\nexport {logout as '007be0fe05adcc7d0c069ca539eb0ff1cb6fd0e443'} from 'ACTIONS_MODULE0'\nexport {getUserProfile as '00e7e2892a7c5df6d1d5fa13dd13a750332361b7bf'} from 'ACTIONS_MODULE0'\nexport {forgotPassword as '605ee68581d93fd51fe0565806b8059b6a037fc225'} from 'ACTIONS_MODULE0'\nexport {registerUser as '6074658acb00601d2549775ad0d80ebfad3207beb6'} from 'ACTIONS_MODULE0'\nexport {loginUser as '6095e1a16a36fae9f991406ee5d3ae93ce05419f13'} from 'ACTIONS_MODULE0'\nexport {changePassword as '60ddee3a1e9f4d6efc9c1cece9c322d5fbc2422f7f'} from 'ACTIONS_MODULE0'\nexport {getJwtInfo as '008dfdacd08dee8b2631add445c74492baff98a2ad'} from 'ACTIONS_MODULE1'\nexport {clearSessionAndBackToLogin as '00e6dbd535aa6e9ad1aa89e2cac9bd8d5cc801465a'} from 'ACTIONS_MODULE1'\nexport {getSession as '4001fad38119db8542322dccd0617b3df1d830a26c'} from 'ACTIONS_MODULE1'\nexport {deleteSession as '403e60a2cf4748152b9343ec01a868c4669796cd15'} from 'ACTIONS_MODULE1'\nexport {verifyJwtToken as '4096ae64ac4ea3209d6dc5820144fc5deef2f95a15'} from 'ACTIONS_MODULE1'\nexport {fetchWithAuth as '60a89ef542525d5dfde77653987c6ed3b387c5216e'} from 'ACTIONS_MODULE1'\nexport {fetchWithoutAuth as '60f988a13a61f71753d0e8e0e1219596262b22d654'} from 'ACTIONS_MODULE1'\nexport {createSession as '70c1d52c2370d1547b5942fa95004975d259c404e8'} from 'ACTIONS_MODULE1'\nexport {getFavoritesCount as '004337d8d5eb6ed7e2919a0aeefac685ea3d2d1941'} from 'ACTIONS_MODULE2'\nexport {getLatestNotifications as '40208af54e01b051461b63d477eaaaa55f04d9b278'} from 'ACTIONS_MODULE3'\nexport {getUnreadCount as '00bbe381627ea72a4cce4f9c30bb837f34cc1bd027'} from 'ACTIONS_MODULE3'\nexport {markAsRead as '40ae052a3c4bb7eb61dbfdc1c4f786ce752c93bed7'} from 'ACTIONS_MODULE3'\n"], "names": [], "mappings": ";AAAA;AAQA;AAQA;AACA", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function parseEmptyStringsToNull(payload) {\r\n  if (Array.isArray(payload)) {\r\n    return payload.map(item => parseEmptyStringsToNull(item));\r\n  }\r\n\r\n  if (typeof payload === 'object' && payload !== null) {\r\n    const newPayload = { ...payload };\r\n\r\n    Object.keys(newPayload).forEach(key => {\r\n      if (newPayload[key] === '') {\r\n        newPayload[key] = null;\r\n      } else if (typeof newPayload[key] === 'object' && newPayload[key] !== null) {\r\n        newPayload[key] = parseEmptyStringsToNull(newPayload[key]);\r\n      }\r\n    });\r\n\r\n    return newPayload;\r\n  }\r\n\r\n  return payload;\r\n}\r\n\r\nexport function formatCurrency(amount) {\r\n  return new Intl.NumberFormat('vi-VN', {\r\n    style: 'currency',\r\n    currency: 'VND',\r\n    maximumFractionDigits: 0\r\n  }).format(amount);\r\n}\r\n\r\nexport function formatDate(dateString) {\r\n  return new Date(dateString).toLocaleDateString('vi-VN', {\r\n    day: '2-digit',\r\n    month: '2-digit',\r\n    year: 'numeric',\r\n    hour: '2-digit',\r\n    minute: '2-digit'\r\n  });\r\n}\r\n\r\nexport const formatPriceShort = (price) => {\r\n  if (price === null || price === undefined) return 'N/A';\r\n  if (price >= 1000000000) {\r\n      // Làm tròn 1 chữ số thập phân, loại bỏ .0 nếu là số nguyên tỷ\r\n      const val = (price / 1000000000).toFixed(1);\r\n      return val.endsWith('.0') ? val.slice(0, -2) + ' Tỷ' : val + ' Tỷ';\r\n  }\r\n  if (price >= 1000000) {\r\n      // Làm tròn 1 chữ số thập phân, loại bỏ .0 nếu là số nguyên triệu\r\n       const val = (price / 1000000).toFixed(1);\r\n      return val.endsWith('.0') ? val.slice(0, -2) + ' Tr' : val + ' Tr';\r\n  }\r\n   // Định dạng số thông thường cho các giá trị nhỏ hơn 1 triệu\r\n   if (typeof price === 'number') {\r\n       return price.toLocaleString('vi-VN');\r\n   }\r\n  return String(price); // Trường hợp khác cố gắng convert sang string\r\n};\r\n\r\nexport function debounce(func, delay) {\r\n  let timeoutId;\r\n  // Hàm debounce trả về một hàm mới\r\n  const debounced = function(...args) {\r\n    const context = this; // Lưu ngữ cảnh 'this'\r\n    clearTimeout(timeoutId); // Xóa timer cũ nếu có\r\n    // Thiết lập timer mới để gọi hàm gốc sau độ trễ\r\n    timeoutId = setTimeout(() => {\r\n      func.apply(context, args); // Gọi hàm gốc với ngữ cảnh và đối số đúng\r\n    }, delay);\r\n  };\r\n\r\n  // Thêm phương thức cancel vào hàm debounced trả về\r\n  debounced.cancel = function() {\r\n    clearTimeout(timeoutId);\r\n  };\r\n\r\n  return debounced; // Trả về hàm đã được debounce\r\n}"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,wBAAwB,OAAO;IAC7C,IAAI,MAAM,OAAO,CAAC,UAAU;QAC1B,OAAO,QAAQ,GAAG,CAAC,CAAA,OAAQ,wBAAwB;IACrD;IAEA,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,MAAM,aAAa;YAAE,GAAG,OAAO;QAAC;QAEhC,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;YAC9B,IAAI,UAAU,CAAC,IAAI,KAAK,IAAI;gBAC1B,UAAU,CAAC,IAAI,GAAG;YACpB,OAAO,IAAI,OAAO,UAAU,CAAC,IAAI,KAAK,YAAY,UAAU,CAAC,IAAI,KAAK,MAAM;gBAC1E,UAAU,CAAC,IAAI,GAAG,wBAAwB,UAAU,CAAC,IAAI;YAC3D;QACF;QAEA,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAAS,eAAe,MAAM;IACnC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,UAAU;IACnC,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAClD,IAAI,SAAS,YAAY;QACrB,8DAA8D;QAC9D,MAAM,MAAM,CAAC,QAAQ,UAAU,EAAE,OAAO,CAAC;QACzC,OAAO,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,QAAQ,MAAM;IACjE;IACA,IAAI,SAAS,SAAS;QAClB,iEAAiE;QAChE,MAAM,MAAM,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC;QACvC,OAAO,IAAI,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,QAAQ,MAAM;IACjE;IACC,4DAA4D;IAC5D,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO,MAAM,cAAc,CAAC;IAChC;IACD,OAAO,OAAO,QAAQ,8CAA8C;AACtE;AAEO,SAAS,SAAS,IAAI,EAAE,KAAK;IAClC,IAAI;IACJ,kCAAkC;IAClC,MAAM,YAAY,SAAS,GAAG,IAAI;QAChC,MAAM,UAAU,IAAI,EAAE,sBAAsB;QAC5C,aAAa,YAAY,sBAAsB;QAC/C,gDAAgD;QAChD,YAAY,WAAW;YACrB,KAAK,KAAK,CAAC,SAAS,OAAO,0CAA0C;QACvE,GAAG;IACL;IAEA,mDAAmD;IACnD,UAAU,MAAM,GAAG;QACjB,aAAa;IACf;IAEA,OAAO,WAAW,8BAA8B;AAClD", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/button.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Button = React.forwardRef(({ className, variant, size, asChild = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"button\"\r\n  return (\r\n    <Comp\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      ref={ref}\r\n      {...props} />\r\n  );\r\n})\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxF,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAEf;AACA,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/switch.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Switch = registerClientReference(\n    function() { throw new Error(\"Attempted to call Switch() from the server but Switch is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/switch.jsx <module evaluation>\",\n    \"Switch\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0DACA", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/switch.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Switch = registerClientReference(\n    function() { throw new Error(\"Attempted to call Switch() from the server but Switch is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/switch.jsx\",\n    \"Switch\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,sCACA", "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/badge.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  ...props\r\n}) {\r\n  return (<div className={cn(badgeVariants({ variant }), className)} {...props} />);\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OACJ;IACC,qBAAQ,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAC9E", "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/card.jsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"rounded-xl border bg-card text-card-foreground shadow\", className)}\r\n    {...props} />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props} />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props} />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props} />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props} />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACtD,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;QACtE,GAAG,KAAK;;;;;;AAEb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAEb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACjE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAEb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/separator.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/separator.jsx <module evaluation>\",\n    \"Separator\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/separator.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/separator.jsx\",\n    \"Separator\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA", "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/%5Blocale%5D/%28protected%29/user/bds/new/success/page.jsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Check<PERSON>heck } from \"lucide-react\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport {Link} from '@/i18n/navigation';;\r\nimport { getTranslations } from \"next-intl/server\";\r\n\r\nconst renderDemo1 = async () => {\r\n  const t = await getTranslations(\"CreatePropertySuccessPage\");\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      <Card>\r\n        <CardHeader className=\"border-b border-border p-4\">\r\n          <CardTitle className=\"text-lg text-center\">\r\n            {t(\"pageTitle\")}\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent className=\"p-6\">\r\n          <div className=\"flex flex-col items-center gap-4\">\r\n            <div className=\"w-12 h-12 rounded-full bg-success flex justify-center items-center\">\r\n              <CheckCheck className=\"text-success-foreground w-6 h-6\" />\r\n            </div>\r\n            <h3 className=\"text-xl font-semibold text-success\">\r\n              {t(\"successMessage\")}\r\n            </h3>\r\n            <div className=\"text-muted-foreground text-sm text-center\">\r\n              {t(\"baseCostLabel\")}\r\n            </div>\r\n            <p className=\"text-center text-sm text-muted-foreground\">\r\n              {t(\"moderationNotice\")}\r\n            </p>\r\n          </div>\r\n          <Separator className=\"my-6\" />\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            <div className=\"space-y-4\">\r\n              <h4 className=\"font-semibold text-base\">{t(\"statusLabel\")}</h4>\r\n              <Badge variant=\"outline\">{t(\"statusPending\")}</Badge>\r\n            </div>\r\n            <div className=\"space-y-4\">\r\n              <h4 className=\"font-semibold text-base\">{t(\"listingIdLabel\")}</h4>\r\n              <p className=\"text-sm text-muted-foreground\">6456465</p>\r\n            </div>\r\n            <div className=\"space-y-4\">\r\n              <h4 className=\"font-semibold text-base\">\r\n                {t(\"displayTimeLabel\")}\r\n              </h4>\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                12/03/2024 - 22/03/2024\r\n              </p>\r\n            </div>\r\n            <div className=\"space-y-4\">\r\n              <h4 className=\"font-semibold text-base\">Phí cơ bản</h4>\r\n              <p className=\"text-sm text-muted-foreground\">{t(\"baseCostLabel\")}</p>\r\n            </div>\r\n          </div>\r\n          <Separator className=\"my-6\" />\r\n          <div>\r\n            <h4 className=\"font-semibold text-base mb-4\">\r\n              {t(\"highlightTitle\")}\r\n            </h4>\r\n            <Card className=\"overflow-hidden\">\r\n              <div className=\"bg-muted/40 p-4\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <h5 className=\"font-medium text-sm\">\r\n                    {t(\"highlightPackageTitle\")}\r\n                  </h5>\r\n                  <Switch defaultChecked />\r\n                </div>\r\n                <p className=\"text-xs text-muted-foreground mt-1\">\r\n                  {t(\"highlightDuration\")}\r\n                </p>\r\n              </div>\r\n              <div className=\"p-4 grid grid-cols-2 gap-4\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <input type=\"radio\" id=\"push3\" name=\"highlight\" value=\"3\" />\r\n                  <label htmlFor=\"push3\" className=\"text-sm\">\r\n                    {t(\"highlightOption3\")}\r\n                  </label>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <input type=\"radio\" id=\"push6\" name=\"highlight\" value=\"6\" />\r\n                  <label htmlFor=\"push6\" className=\"text-sm\">\r\n                    {t(\"highlightOption6\")}\r\n                  </label>\r\n                </div>\r\n              </div>\r\n            </Card>\r\n            <Card className=\"overflow-hidden mt-4\">\r\n              <div className=\"bg-muted/40 p-4\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <h5 className=\"font-medium text-sm\">{t(\"autoRenewTitle\")}</h5>\r\n                  <Switch defaultChecked />\r\n                </div>\r\n                <p className=\"text-xs text-muted-foreground mt-1\">\r\n                  {t(\"autoRenewDescription\")}\r\n                </p>\r\n              </div>\r\n            </Card>\r\n          </div>\r\n          <Separator className=\"my-6\" />\r\n          <div className=\"flex justify-end gap-4\">\r\n            <Button variant=\"outline\" asChild>\r\n              <Link href=\"../new\">{t(\"postAnotherButton\")}</Link>\r\n            </Button>\r\n            <Button asChild>\r\n              <Link href=\"../\">{t(\"manageListingsButton\")}</Link>\r\n            </Button>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst CreateSuccess = () => {\r\n  return <>{renderDemo1()}</>;\r\n};\r\n\r\nexport default CreateSuccess;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,MAAM,cAAc;IAClB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;IAEhC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,yHAAA,CAAA,OAAI;;8BACH,8OAAC,yHAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,8OAAC,yHAAA,CAAA,YAAS;wBAAC,WAAU;kCAClB,EAAE;;;;;;;;;;;8BAGP,8OAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAI,WAAU;8CACZ,EAAE;;;;;;8CAEL,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAGP,8OAAC,8HAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2B,EAAE;;;;;;sDAC3C,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW,EAAE;;;;;;;;;;;;8CAE9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2B,EAAE;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAE/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAE,WAAU;sDAAiC,EAAE;;;;;;;;;;;;;;;;;;sCAGpD,8OAAC,8HAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,EAAE;;;;;;sEAEL,8OAAC,2HAAA,CAAA,SAAM;4DAAC,cAAc;;;;;;;;;;;;8DAExB,8OAAC;oDAAE,WAAU;8DACV,EAAE;;;;;;;;;;;;sDAGP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,MAAK;4DAAQ,IAAG;4DAAQ,MAAK;4DAAY,OAAM;;;;;;sEACtD,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAC9B,EAAE;;;;;;;;;;;;8DAGP,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,MAAK;4DAAQ,IAAG;4DAAQ,MAAK;4DAAY,OAAM;;;;;;sEACtD,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAC9B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;8CAKX,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuB,EAAE;;;;;;kEACvC,8OAAC,2HAAA,CAAA,SAAM;wDAAC,cAAc;;;;;;;;;;;;0DAExB,8OAAC;gDAAE,WAAU;0DACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAKX,8OAAC,8HAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,OAAO;8CAC/B,cAAA,8OAAC,kHAAA,CAAA,OAAI;wCAAC,MAAK;kDAAU,EAAE;;;;;;;;;;;8CAEzB,8OAAC,2HAAA,CAAA,SAAM;oCAAC,OAAO;8CACb,cAAA,8OAAC,kHAAA,CAAA,OAAI;wCAAC,MAAK;kDAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC;AAEA,MAAM,gBAAgB;IACpB,qBAAO;kBAAG;;AACZ;uCAEe", "debugId": null}}]}