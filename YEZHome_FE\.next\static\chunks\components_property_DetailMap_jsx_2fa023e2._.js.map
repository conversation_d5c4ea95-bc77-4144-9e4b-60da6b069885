{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/property/DetailMap.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useRef } from \"react\";\r\nimport goongjs from \"@goongmaps/goong-js\";\r\nimport \"@goongmaps/goong-js/dist/goong-js.css\";\r\n\r\nconst isBrowser = typeof window !== \"undefined\";\r\n\r\nconst DetailMap = ({ property, center }) => {\r\n  const mapContainerRef = useRef(null);\r\n  const mapRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    if (!isBrowser || !mapContainerRef.current || !center) {\r\n      if (mapRef.current) {\r\n        mapRef.current.remove();\r\n        mapRef.current = null;\r\n      }\r\n      return;\r\n    }\r\n\r\n    goongjs.accessToken = `${process.env.NEXT_PUBLIC_GOONG_MAPTILES_KEY}`;\r\n\r\n    const map = mapRef.current;\r\n\r\n    if (!map) {\r\n      // Khởi tạo bản đồ lần đầu tiên với vị trí center nhận được qua prop\r\n      const goongMap = new goongjs.Map({\r\n        container: mapContainerRef.current,\r\n        style: \"https://tiles.goong.io/assets/goong_map_web.json\",\r\n        center: [center.longitude, center.latitude],\r\n        zoom: 15,\r\n      });\r\n\r\n      mapRef.current = goongMap;\r\n    } else {\r\n      // Nếu bản đồ đã được khởi tạo, chỉ cập nhật vị trí trung tâm nếu prop center thay đổi\r\n      const currentCenter = mapRef.current.getCenter();\r\n      const currentZoom = mapRef.current.getZoom();\r\n\r\n      const centerHasChanged =\r\n        currentCenter.lat !== center.latitude || currentCenter.lng !== center.longitude;\r\n      const zoomHasChanged = currentZoom !== zoom;\r\n\r\n      if (centerHasChanged || zoomHasChanged) {\r\n        map.flyTo({\r\n          center: [center.longitude, center.latitude],\r\n          zoom: zoom,\r\n          essential: true,\r\n        });\r\n      } else {\r\n        console.log(\r\n          \"GoongMap Effect: Prop center/zoom không đổi so với trạng thái hiện tại, BỎ QUA cập nhật bản đồ.\"\r\n        );\r\n      }\r\n    }\r\n\r\n    return () => {\r\n      if (mapRef.current) {\r\n        mapRef.current.remove();\r\n        mapRef.current = null;\r\n      }\r\n    };\r\n  }, [center]);\r\n\r\n  // Effect dọn dẹp bản đồ cuối cùng khi unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (mapRef.current) {\r\n        mapRef.current.remove();\r\n        mapRef.current = null;\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // --- Effect xử lý Marker ---\r\n  useEffect(() => {\r\n    if (!mapRef.current || !property) {\r\n      return;\r\n    }\r\n\r\n    const map = mapRef.current;\r\n\r\n    // Xóa marker cũ\r\n    if (map._currentMarkers) {\r\n      map._currentMarkers.forEach((marker) => marker.remove());\r\n    }\r\n    map._currentMarkers = [];\r\n\r\n    if (property) {\r\n      if (property.latitude !== undefined && property.longitude !== undefined) {\r\n        try {\r\n          // --- TẠO PHẦN TỬ DOM TÙY CHỈNH CHO MARKER ---\r\n          const el = document.createElement(\"div\");\r\n          el.className = \"marker\";\r\n          el.style.backgroundImage = \"url(/detail_marker.png)\";\r\n          el.style.width = \"50px\";\r\n          el.style.height = \"50px\";\r\n          el.style.backgroundSize = \"cover\";\r\n          el.style.cursor = \"pointer\";\r\n\r\n          const marker = new goongjs.Marker(el, { anchor: \"bottom\" })\r\n            .setLngLat([property.longitude, property.latitude])\r\n            .addTo(map);\r\n\r\n          map._currentMarkers.push(marker);\r\n        } catch (markerError) {\r\n          console.error(\r\n            `GoongMap Effect Markers: Lỗi khi tạo hoặc thêm marker:`,\r\n            property,\r\n            markerError\r\n          );\r\n        }\r\n      }\r\n    } else {\r\n      console.log(\"GoongMap Effect Marker: Không có marker để thêm.\");\r\n    }\r\n\r\n    // Cleanup cho markers (xóa markers khi prop markers thay đổi hoặc unmount)\r\n    return () => {\r\n      if (mapRef.current && mapRef.current._currentMarkers) {\r\n        mapRef.current._currentMarkers.forEach((marker) => marker.remove());\r\n      }\r\n    };\r\n  }, [property]); // Dependencies: Chỉ chạy lại khi prop markers thay đổi\r\n\r\n  return (\r\n    <div className={`w-full h-[350px] relative z-0 opacity-75 cursor-not-allowed`}>\r\n      <div ref={mapContainerRef} className=\"w-full h-full\" />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetailMap;\r\n"], "names": [], "mappings": ";;;AAqB6B;;AAnB7B;AACA;;;AAHA;;;;AAMA,MAAM,YAAY,aAAkB;AAEpC,MAAM,YAAY,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE;;IACrC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,aAAa,CAAC,gBAAgB,OAAO,IAAI,CAAC,QAAQ;gBACrD,IAAI,OAAO,OAAO,EAAE;oBAClB,OAAO,OAAO,CAAC,MAAM;oBACrB,OAAO,OAAO,GAAG;gBACnB;gBACA;YACF;YAEA,oKAAA,CAAA,UAAO,CAAC,WAAW,GAAG,+EAA+C;YAErE,MAAM,MAAM,OAAO,OAAO;YAE1B,IAAI,CAAC,KAAK;gBACR,oEAAoE;gBACpE,MAAM,WAAW,IAAI,oKAAA,CAAA,UAAO,CAAC,GAAG,CAAC;oBAC/B,WAAW,gBAAgB,OAAO;oBAClC,OAAO;oBACP,QAAQ;wBAAC,OAAO,SAAS;wBAAE,OAAO,QAAQ;qBAAC;oBAC3C,MAAM;gBACR;gBAEA,OAAO,OAAO,GAAG;YACnB,OAAO;gBACL,sFAAsF;gBACtF,MAAM,gBAAgB,OAAO,OAAO,CAAC,SAAS;gBAC9C,MAAM,cAAc,OAAO,OAAO,CAAC,OAAO;gBAE1C,MAAM,mBACJ,cAAc,GAAG,KAAK,OAAO,QAAQ,IAAI,cAAc,GAAG,KAAK,OAAO,SAAS;gBACjF,MAAM,iBAAiB,gBAAgB;gBAEvC,IAAI,oBAAoB,gBAAgB;oBACtC,IAAI,KAAK,CAAC;wBACR,QAAQ;4BAAC,OAAO,SAAS;4BAAE,OAAO,QAAQ;yBAAC;wBAC3C,MAAM;wBACN,WAAW;oBACb;gBACF,OAAO;oBACL,QAAQ,GAAG,CACT;gBAEJ;YACF;YAEA;uCAAO;oBACL,IAAI,OAAO,OAAO,EAAE;wBAClB,OAAO,OAAO,CAAC,MAAM;wBACrB,OAAO,OAAO,GAAG;oBACnB;gBACF;;QACF;8BAAG;QAAC;KAAO;IAEX,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;uCAAO;oBACL,IAAI,OAAO,OAAO,EAAE;wBAClB,OAAO,OAAO,CAAC,MAAM;wBACrB,OAAO,OAAO,GAAG;oBACnB;gBACF;;QACF;8BAAG,EAAE;IAEL,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,UAAU;gBAChC;YACF;YAEA,MAAM,MAAM,OAAO,OAAO;YAE1B,gBAAgB;YAChB,IAAI,IAAI,eAAe,EAAE;gBACvB,IAAI,eAAe,CAAC,OAAO;2CAAC,CAAC,SAAW,OAAO,MAAM;;YACvD;YACA,IAAI,eAAe,GAAG,EAAE;YAExB,IAAI,UAAU;gBACZ,IAAI,SAAS,QAAQ,KAAK,aAAa,SAAS,SAAS,KAAK,WAAW;oBACvE,IAAI;wBACF,+CAA+C;wBAC/C,MAAM,KAAK,SAAS,aAAa,CAAC;wBAClC,GAAG,SAAS,GAAG;wBACf,GAAG,KAAK,CAAC,eAAe,GAAG;wBAC3B,GAAG,KAAK,CAAC,KAAK,GAAG;wBACjB,GAAG,KAAK,CAAC,MAAM,GAAG;wBAClB,GAAG,KAAK,CAAC,cAAc,GAAG;wBAC1B,GAAG,KAAK,CAAC,MAAM,GAAG;wBAElB,MAAM,SAAS,IAAI,oKAAA,CAAA,UAAO,CAAC,MAAM,CAAC,IAAI;4BAAE,QAAQ;wBAAS,GACtD,SAAS,CAAC;4BAAC,SAAS,SAAS;4BAAE,SAAS,QAAQ;yBAAC,EACjD,KAAK,CAAC;wBAET,IAAI,eAAe,CAAC,IAAI,CAAC;oBAC3B,EAAE,OAAO,aAAa;wBACpB,QAAQ,KAAK,CACX,CAAC,sDAAsD,CAAC,EACxD,UACA;oBAEJ;gBACF;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,2EAA2E;YAC3E;uCAAO;oBACL,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,eAAe,EAAE;wBACpD,OAAO,OAAO,CAAC,eAAe,CAAC,OAAO;mDAAC,CAAC,SAAW,OAAO,MAAM;;oBAClE;gBACF;;QACF;8BAAG;QAAC;KAAS,GAAG,uDAAuD;IAEvE,qBACE,6LAAC;QAAI,WAAW,CAAC,2DAA2D,CAAC;kBAC3E,cAAA,6LAAC;YAAI,KAAK;YAAiB,WAAU;;;;;;;;;;;AAG3C;GA3HM;KAAA;uCA6HS", "debugId": null}}]}