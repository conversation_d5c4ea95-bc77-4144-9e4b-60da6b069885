(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/lucide-react/dist/esm/icons/expand.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Expand)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "m21 21-6-6m6 6v-4.8m0 4.8h-4.8",
            key: "1c15vz"
        }
    ],
    [
        "path",
        {
            d: "M3 16.2V21m0 0h4.8M3 21l6-6",
            key: "1fsnz2"
        }
    ],
    [
        "path",
        {
            d: "M21 7.8V3m0 0h-4.8M21 3l-6 6",
            key: "hawz9i"
        }
    ],
    [
        "path",
        {
            d: "M3 7.8V3m0 0h4.8M3 3l6 6",
            key: "u9ee12"
        }
    ]
];
const Expand = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("Expand", __iconNode);
;
 //# sourceMappingURL=expand.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/expand.js [app-client] (ecmascript) <export default as Expand>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Expand": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$expand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$expand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/expand.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/react-photo-album/dist/client/hooks.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useArray": (()=>useArray),
    "useContainerWidth": (()=>useContainerWidth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
"use client";
;
function useArray(array) {
    const ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(array);
    if (!array || !ref.current || array.length !== ref.current.length || ref.current.some((el, i)=>el !== array[i])) {
        ref.current = array;
    }
    return ref.current;
}
function containerWidthReducer(state, [newContainerWidth, newScrollbarWidth]) {
    const [containerWidth, scrollbarWidth] = state;
    if (containerWidth !== void 0 && scrollbarWidth !== void 0 && newContainerWidth !== void 0 && newScrollbarWidth !== void 0 && newContainerWidth > containerWidth && newContainerWidth - containerWidth <= 20 && newScrollbarWidth < scrollbarWidth) {
        return [
            containerWidth,
            newScrollbarWidth
        ];
    }
    return containerWidth !== newContainerWidth || scrollbarWidth !== newScrollbarWidth ? [
        newContainerWidth,
        newScrollbarWidth
    ] : state;
}
function resolveContainerWidth(el, breakpoints) {
    let width = el?.clientWidth;
    if (width !== void 0 && breakpoints && breakpoints.length > 0) {
        const sorted = [
            ...breakpoints.filter((x)=>x > 0)
        ].sort((a, b)=>b - a);
        sorted.push(Math.floor(sorted[sorted.length - 1] / 2));
        width = sorted.find((breakpoint, index)=>breakpoint <= width || index === sorted.length - 1);
    }
    return width;
}
function useContainerWidth(ref, breakpointsArray, defaultContainerWidth) {
    const [[containerWidth], dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReducer"])(containerWidthReducer, [
        defaultContainerWidth
    ]);
    const breakpoints = useArray(breakpointsArray);
    const observerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(void 0);
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContainerWidth.useCallback[containerRef]": (node)=>{
            observerRef.current?.disconnect();
            observerRef.current = void 0;
            const updateWidth = {
                "useContainerWidth.useCallback[containerRef].updateWidth": ()=>dispatch([
                        resolveContainerWidth(node, breakpoints),
                        window.innerWidth - document.documentElement.clientWidth
                    ])
            }["useContainerWidth.useCallback[containerRef].updateWidth"];
            updateWidth();
            if (node && typeof ResizeObserver !== "undefined") {
                observerRef.current = new ResizeObserver(updateWidth);
                observerRef.current.observe(node);
            }
            if (typeof ref === "function") {
                ref(node);
            } else if (ref) {
                ref.current = node;
            }
        }
    }["useContainerWidth.useCallback[containerRef]"], [
        ref,
        breakpoints
    ]);
    return {
        containerRef,
        containerWidth
    };
}
;
}}),
"[project]/node_modules/react-photo-album/dist/utils/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clsx": (()=>clsx),
    "cssClass": (()=>cssClass),
    "cssVar": (()=>cssVar),
    "ratio": (()=>ratio),
    "resolveCommonProps": (()=>resolveCommonProps),
    "resolveResponsiveParameter": (()=>resolveResponsiveParameter),
    "round": (()=>round),
    "srcSetAndSizes": (()=>srcSetAndSizes),
    "unwrap": (()=>unwrap),
    "unwrapParameter": (()=>unwrapParameter)
});
function clsx(...classes) {
    return [
        ...classes
    ].filter(Boolean).join(" ");
}
function cssClass(suffix) {
    return [
        "react-photo-album",
        suffix
    ].filter(Boolean).join("--");
}
function cssVar(suffix) {
    return `--${cssClass(suffix)}`;
}
function ratio({ width, height }) {
    return width / height;
}
const breakpoints = Object.freeze([
    1200,
    600,
    300,
    0
]);
function unwrap(value, arg) {
    return typeof value === "function" ? value(arg) : value;
}
function unwrapParameter(value, containerWidth) {
    return containerWidth !== void 0 ? unwrap(value, containerWidth) : void 0;
}
function selectResponsiveValue(values, containerWidth) {
    const index = breakpoints.findIndex((breakpoint)=>breakpoint <= containerWidth);
    return unwrap(values[Math.max(index, 0)], containerWidth);
}
function resolveResponsiveParameter(parameter, containerWidth, values, minValue = 0) {
    if (containerWidth === void 0) return void 0;
    const value = unwrapParameter(parameter, containerWidth);
    return Math.round(Math.max(value === void 0 ? selectResponsiveValue(values, containerWidth) : value, minValue));
}
function resolveCommonProps(containerWidth, { spacing, padding, componentsProps, render }) {
    return {
        spacing: resolveResponsiveParameter(spacing, containerWidth, [
            20,
            15,
            10,
            5
        ]),
        padding: resolveResponsiveParameter(padding, containerWidth, [
            0,
            0,
            0,
            0
        ]),
        componentsProps: unwrap(componentsProps, containerWidth) || {},
        render: unwrap(render, containerWidth)
    };
}
function round(value, decimals = 0) {
    const factor = 10 ** decimals;
    return Math.round((value + Number.EPSILON) * factor) / factor;
}
function srcSetAndSizes(photo, responsiveSizes, photoWidth, containerWidth, photosCount, spacing, padding) {
    let srcSet;
    let sizes;
    const calcSizes = (base)=>{
        const gaps = spacing * (photosCount - 1) + 2 * padding * photosCount;
        return `calc((${base.match(/^\s*calc\((.*)\)\s*$/)?.[1] ?? base} - ${gaps}px) / ${round((containerWidth - gaps) / photoWidth, 5)})`;
    };
    const images = photo.srcSet;
    if (images && images.length > 0) {
        srcSet = images.concat(!images.some(({ width })=>width === photo.width) ? [
            {
                src: photo.src,
                width: photo.width,
                height: photo.height
            }
        ] : []).sort((first, second)=>first.width - second.width).map((image)=>`${image.src} ${image.width}w`).join(", ");
    }
    if (responsiveSizes?.size) {
        sizes = (responsiveSizes.sizes || []).map(({ viewport, size })=>`${viewport} ${calcSizes(size)}`).concat(calcSizes(responsiveSizes.size)).join(", ");
    } else {
        sizes = `${Math.ceil(photoWidth / containerWidth * 100)}vw`;
    }
    return {
        srcSet,
        sizes
    };
}
;
}}),
"[project]/node_modules/react-photo-album/dist/static/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>StaticPhotoAlbum$1)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-photo-album/dist/utils/index.js [app-client] (ecmascript)");
;
;
;
function Component({ as, render, context, classes = [], variables = {}, style: styleProp, className: classNameProp, children, ...rest }, ref) {
    const className = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(...(Array.isArray(classes) ? classes : [
        classes
    ]).filter((el)=>typeof el === "string").map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cssClass"]), classNameProp);
    const style = {
        ...Object.fromEntries(Object.entries(variables).map(([key, value])=>[
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cssVar"])(key.replace(/([a-z])([A-Z])/g, "$1-$2").toLowerCase()),
                typeof value === "number" ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["round"])(value, 5) : value
            ])),
        ...styleProp
    };
    const props = {
        style,
        className,
        children,
        ...rest
    };
    if (render) {
        const rendered = render({
            ref,
            ...props
        }, context);
        if (rendered) return rendered;
    }
    const Element = as || "div";
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Element, {
        ref,
        ...props
    });
}
const Component$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(Component);
function PhotoComponent({ photo, index, width, height, onClick, render: { wrapper, link, button, image, extras } = {}, componentsProps: { link: linkProps, button: buttonProps, wrapper: wrapperProps, image: imageProps } = {} }, ref) {
    const { href } = photo;
    const context = {
        photo,
        index,
        width: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["round"])(width, 3),
        height: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["round"])(height, 3)
    };
    let props;
    if (href) {
        props = {
            ...linkProps,
            as: "a",
            render: link,
            classes: [
                "photo",
                "link"
            ],
            href,
            onClick
        };
    } else if (onClick) {
        props = {
            ...buttonProps,
            as: "button",
            type: "button",
            render: button,
            classes: [
                "photo",
                "button"
            ],
            onClick
        };
    } else {
        props = {
            ...wrapperProps,
            render: wrapper,
            classes: "photo"
        };
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(Component$1, {
        ref,
        variables: {
            photoWidth: context.width,
            photoHeight: context.height
        },
        ...{
            context,
            ...props
        },
        children: [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Component$1, {
                as: "img",
                classes: "image",
                render: image,
                context,
                ...imageProps
            }),
            extras?.({}, context)
        ]
    });
}
const PhotoComponent$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(PhotoComponent);
function StaticPhotoAlbum({ layout, sizes, model, skeleton, onClick: onClickCallback, render: { container, track, photo: renderPhoto, ...restRender } = {}, componentsProps: { container: containerProps, track: trackProps, link: linkProps, button: buttonProps, wrapper: wrapperProps, image: imageProps } = {} }, ref) {
    const { spacing, padding, containerWidth, tracks, variables, horizontal } = model || {};
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(Component$1, {
        role: "group",
        "aria-label": "Photo album",
        ...containerProps,
        variables: {
            spacing,
            padding,
            containerWidth,
            ...variables
        },
        classes: [
            "",
            layout
        ],
        render: container,
        ref,
        children: [
            spacing !== void 0 && padding !== void 0 && containerWidth !== void 0 && tracks?.map(({ photos, variables: trackVariables }, trackIndex)=>{
                const trackSize = photos.length;
                const photosCount = horizontal ? trackSize : tracks.length;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(Component$1, {
                    ...trackProps,
                    key: trackIndex,
                    render: track,
                    classes: "track",
                    variables: {
                        trackSize,
                        ...trackVariables
                    }
                }, photos.map((context)=>{
                    const { photo, index, width } = context;
                    const { key, src, alt, title, label } = photo;
                    const onClick = onClickCallback ? (event)=>{
                        onClickCallback({
                            event,
                            photo,
                            index
                        });
                    } : void 0;
                    if (renderPhoto) {
                        const rendered = renderPhoto({
                            onClick
                        }, context);
                        if (rendered) return rendered;
                    }
                    const ariaLabel = (props)=>{
                        return label ? {
                            "aria-label": label,
                            ...props
                        } : props;
                    };
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(PhotoComponent$1, {
                        onClick,
                        render: restRender,
                        componentsProps: {
                            image: {
                                loading: "lazy",
                                decoding: "async",
                                src,
                                alt,
                                title,
                                ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["srcSetAndSizes"])(photo, sizes, width, containerWidth, photosCount, spacing, padding),
                                ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unwrap"])(imageProps, context)
                            },
                            link: ariaLabel((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unwrap"])(linkProps, context)),
                            button: ariaLabel((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unwrap"])(buttonProps, context)),
                            wrapper: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unwrap"])(wrapperProps, context)
                        },
                        ...context
                    }, key ?? src);
                }));
            }),
            containerWidth === void 0 && skeleton
        ]
    });
}
const StaticPhotoAlbum$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(StaticPhotoAlbum);
;
}}),
"[project]/node_modules/react-photo-album/dist/client/rowsProps.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>resolveRowsProps)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-photo-album/dist/utils/index.js [app-client] (ecmascript)");
;
function resolveRowsProps(containerWidth, { photos, targetRowHeight, rowConstraints, ...rest }) {
    const { spacing, padding, componentsProps, render } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolveCommonProps"])(containerWidth, rest);
    const { singleRowMaxHeight, minPhotos, maxPhotos } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unwrapParameter"])(rowConstraints, containerWidth) || {};
    if (singleRowMaxHeight !== void 0 && spacing !== void 0 && padding !== void 0) {
        const maxWidth = Math.floor(photos.reduce((acc, { width, height })=>acc + width / height * singleRowMaxHeight - 2 * padding, padding * photos.length * 2 + spacing * (photos.length - 1)));
        if (maxWidth > 0) {
            componentsProps.container = {
                ...componentsProps.container
            };
            componentsProps.container.style = {
                maxWidth,
                ...componentsProps.container.style
            };
        }
    }
    return {
        ...rest,
        targetRowHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resolveResponsiveParameter"])(targetRowHeight, containerWidth, [
            (w)=>w / 5,
            (w)=>w / 4,
            (w)=>w / 3,
            (w)=>w / 2
        ]),
        render,
        spacing,
        padding,
        minPhotos,
        maxPhotos,
        componentsProps
    };
}
;
}}),
"[project]/node_modules/react-photo-album/dist/layouts/rows.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>computeRowsLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-photo-album/dist/utils/index.js [app-client] (ecmascript)");
;
function rankingFunctionComparator(rank) {
    return (a, b)=>rank(b) - rank(a);
}
function MinHeap(comparator) {
    let n = 0;
    const heap = [];
    const greater = (i, j)=>comparator(heap[i], heap[j]) < 0;
    const swap = (i, j)=>{
        const temp = heap[i];
        heap[i] = heap[j];
        heap[j] = temp;
    };
    const swim = (i)=>{
        let k = i;
        let k2 = Math.floor(k / 2);
        while(k > 1 && greater(k2, k)){
            swap(k2, k);
            k = k2;
            k2 = Math.floor(k / 2);
        }
    };
    const sink = (i)=>{
        let k = i;
        let k2 = k * 2;
        while(k2 <= n){
            if (k2 < n && greater(k2, k2 + 1)) k2 += 1;
            if (!greater(k, k2)) break;
            swap(k, k2);
            k = k2;
            k2 = k * 2;
        }
    };
    const push = (element)=>{
        n += 1;
        heap[n] = element;
        swim(n);
    };
    const pop = ()=>{
        if (n === 0) return void 0;
        swap(1, n);
        n -= 1;
        const max = heap.pop();
        sink(1);
        return max;
    };
    const size = ()=>n;
    return {
        push,
        pop,
        size
    };
}
function buildPrecedentsMap(graph, startNode, endNode) {
    const precedentsMap = /* @__PURE__ */ new Map();
    const visited = /* @__PURE__ */ new Set();
    const storedShortestPaths = /* @__PURE__ */ new Map();
    storedShortestPaths.set(startNode, 0);
    const queue = MinHeap(rankingFunctionComparator((el)=>el[1]));
    queue.push([
        startNode,
        0
    ]);
    while(queue.size() > 0){
        const [id, weight] = queue.pop();
        if (!visited.has(id)) {
            const neighboringNodes = graph(id);
            visited.add(id);
            neighboringNodes.forEach((neighborWeight, neighbor)=>{
                const newWeight = weight + neighborWeight;
                const currentId = precedentsMap.get(neighbor);
                const currentWeight = storedShortestPaths.get(neighbor);
                if (currentWeight === void 0 || currentWeight > newWeight && (currentWeight / newWeight > 1.005 || currentId !== void 0 && currentId < id)) {
                    storedShortestPaths.set(neighbor, newWeight);
                    queue.push([
                        neighbor,
                        newWeight
                    ]);
                    precedentsMap.set(neighbor, id);
                }
            });
        }
    }
    return storedShortestPaths.has(endNode) ? precedentsMap : void 0;
}
function getPathFromPrecedentsMap(precedentsMap, endNode) {
    if (!precedentsMap) return void 0;
    const nodes = [];
    for(let node = endNode; node !== void 0; node = precedentsMap.get(node)){
        nodes.push(node);
    }
    return nodes.reverse();
}
function findShortestPath(graph, startNode, endNode) {
    return getPathFromPrecedentsMap(buildPrecedentsMap(graph, startNode, endNode), endNode);
}
function findIdealNodeSearch(photos, containerWidth, targetRowHeight, minPhotos) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["round"])(containerWidth / targetRowHeight / Math.min(...photos.map((photo)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ratio"])(photo)))) + (minPhotos || 0) + 2;
}
function getCommonHeight(photos, containerWidth, spacing, padding) {
    return (containerWidth - (photos.length - 1) * spacing - 2 * padding * photos.length) / photos.reduce((acc, photo)=>acc + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ratio"])(photo), 0);
}
function cost(photos, i, j, width, spacing, padding, targetRowHeight) {
    const row = photos.slice(i, j);
    const commonHeight = getCommonHeight(row, width, spacing, padding);
    return commonHeight > 0 ? (commonHeight - targetRowHeight) ** 2 * row.length : void 0;
}
function makeGetRowNeighbors(photos, spacing, padding, containerWidth, targetRowHeight, limitNodeSearch, minPhotos, maxPhotos) {
    return (node)=>{
        const results = /* @__PURE__ */ new Map();
        results.set(node, 0);
        const startOffset = minPhotos || 1;
        const endOffset = Math.min(limitNodeSearch, maxPhotos || Infinity);
        for(let i = node + startOffset; i < photos.length + 1; i += 1){
            if (i - node > endOffset) break;
            const currentCost = cost(photos, node, i, containerWidth, spacing, padding, targetRowHeight);
            if (currentCost === void 0) break;
            results.set(i, currentCost);
        }
        return results;
    };
}
function computeRowsLayout(photos, spacing, padding, containerWidth, targetRowHeight, minPhotos, maxPhotos) {
    const limitNodeSearch = findIdealNodeSearch(photos, containerWidth, targetRowHeight, minPhotos);
    const getNeighbors = makeGetRowNeighbors(photos, spacing, padding, containerWidth, targetRowHeight, limitNodeSearch, minPhotos, maxPhotos);
    const path = findShortestPath(getNeighbors, 0, photos.length);
    if (!path) return void 0;
    const tracks = [];
    for(let i = 1; i < path.length; i += 1){
        const row = photos.map((photo, index)=>({
                photo,
                index
            })).slice(path[i - 1], path[i]);
        const height = getCommonHeight(row.map(({ photo })=>photo), containerWidth, spacing, padding);
        tracks.push({
            photos: row.map(({ photo, index })=>({
                    photo,
                    index,
                    width: height * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ratio"])(photo),
                    height
                }))
        });
    }
    return {
        spacing,
        padding,
        containerWidth,
        tracks,
        horizontal: true
    };
}
;
}}),
"[project]/node_modules/react-photo-album/dist/client/rows.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RowsPhotoAlbum$1)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$client$2f$hooks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-photo-album/dist/client/hooks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$static$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-photo-album/dist/static/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$client$2f$rowsProps$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-photo-album/dist/client/rowsProps.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$layouts$2f$rows$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-photo-album/dist/layouts/rows.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
function RowsPhotoAlbum({ photos, breakpoints, defaultContainerWidth, ...rest }, ref) {
    const { containerRef, containerWidth } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$client$2f$hooks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContainerWidth"])(ref, breakpoints, defaultContainerWidth);
    const { spacing, padding, targetRowHeight, minPhotos, maxPhotos, ...restProps } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$client$2f$rowsProps$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(containerWidth, {
        photos,
        ...rest
    });
    const model = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "RowsPhotoAlbum.useMemo[model]": ()=>containerWidth !== void 0 && spacing !== void 0 && padding !== void 0 && targetRowHeight !== void 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$layouts$2f$rows$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(photos, spacing, padding, containerWidth, targetRowHeight, minPhotos, maxPhotos) : void 0
    }["RowsPhotoAlbum.useMemo[model]"], [
        photos,
        spacing,
        padding,
        containerWidth,
        targetRowHeight,
        minPhotos,
        maxPhotos
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$static$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        layout: "rows",
        ref: containerRef,
        model,
        ...restProps
    });
}
const RowsPhotoAlbum$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(RowsPhotoAlbum);
;
}}),
"[project]/node_modules/react-photo-album/dist/client/rows.js [app-client] (ecmascript) <export default as RowsPhotoAlbum>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "RowsPhotoAlbum": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$client$2f$rows$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$album$2f$dist$2f$client$2f$rows$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-photo-album/dist/client/rows.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/yet-another-react-lightbox/dist/types.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ACTION_CLOSE": (()=>ACTION_CLOSE),
    "ACTION_NEXT": (()=>ACTION_NEXT),
    "ACTION_PREV": (()=>ACTION_PREV),
    "ACTION_SWIPE": (()=>ACTION_SWIPE),
    "ACTIVE_SLIDE_COMPLETE": (()=>ACTIVE_SLIDE_COMPLETE),
    "ACTIVE_SLIDE_ERROR": (()=>ACTIVE_SLIDE_ERROR),
    "ACTIVE_SLIDE_LOADING": (()=>ACTIVE_SLIDE_LOADING),
    "ACTIVE_SLIDE_PLAYING": (()=>ACTIVE_SLIDE_PLAYING),
    "CLASS_FLEX_CENTER": (()=>CLASS_FLEX_CENTER),
    "CLASS_FULLSIZE": (()=>CLASS_FULLSIZE),
    "CLASS_NO_SCROLL": (()=>CLASS_NO_SCROLL),
    "CLASS_NO_SCROLL_PADDING": (()=>CLASS_NO_SCROLL_PADDING),
    "CLASS_SLIDE_WRAPPER": (()=>CLASS_SLIDE_WRAPPER),
    "CLASS_SLIDE_WRAPPER_INTERACTIVE": (()=>CLASS_SLIDE_WRAPPER_INTERACTIVE),
    "ELEMENT_BUTTON": (()=>ELEMENT_BUTTON),
    "ELEMENT_ICON": (()=>ELEMENT_ICON),
    "EVENT_ON_KEY_DOWN": (()=>EVENT_ON_KEY_DOWN),
    "EVENT_ON_KEY_UP": (()=>EVENT_ON_KEY_UP),
    "EVENT_ON_POINTER_CANCEL": (()=>EVENT_ON_POINTER_CANCEL),
    "EVENT_ON_POINTER_DOWN": (()=>EVENT_ON_POINTER_DOWN),
    "EVENT_ON_POINTER_LEAVE": (()=>EVENT_ON_POINTER_LEAVE),
    "EVENT_ON_POINTER_MOVE": (()=>EVENT_ON_POINTER_MOVE),
    "EVENT_ON_POINTER_UP": (()=>EVENT_ON_POINTER_UP),
    "EVENT_ON_WHEEL": (()=>EVENT_ON_WHEEL),
    "IMAGE_FIT_CONTAIN": (()=>IMAGE_FIT_CONTAIN),
    "IMAGE_FIT_COVER": (()=>IMAGE_FIT_COVER),
    "MODULE_CAROUSEL": (()=>MODULE_CAROUSEL),
    "MODULE_CONTROLLER": (()=>MODULE_CONTROLLER),
    "MODULE_NAVIGATION": (()=>MODULE_NAVIGATION),
    "MODULE_NO_SCROLL": (()=>MODULE_NO_SCROLL),
    "MODULE_PORTAL": (()=>MODULE_PORTAL),
    "MODULE_ROOT": (()=>MODULE_ROOT),
    "MODULE_TOOLBAR": (()=>MODULE_TOOLBAR),
    "PLUGIN_CAPTIONS": (()=>PLUGIN_CAPTIONS),
    "PLUGIN_COUNTER": (()=>PLUGIN_COUNTER),
    "PLUGIN_DOWNLOAD": (()=>PLUGIN_DOWNLOAD),
    "PLUGIN_FULLSCREEN": (()=>PLUGIN_FULLSCREEN),
    "PLUGIN_INLINE": (()=>PLUGIN_INLINE),
    "PLUGIN_SHARE": (()=>PLUGIN_SHARE),
    "PLUGIN_SLIDESHOW": (()=>PLUGIN_SLIDESHOW),
    "PLUGIN_THUMBNAILS": (()=>PLUGIN_THUMBNAILS),
    "PLUGIN_ZOOM": (()=>PLUGIN_ZOOM),
    "SLIDE_STATUS_COMPLETE": (()=>SLIDE_STATUS_COMPLETE),
    "SLIDE_STATUS_ERROR": (()=>SLIDE_STATUS_ERROR),
    "SLIDE_STATUS_LOADING": (()=>SLIDE_STATUS_LOADING),
    "SLIDE_STATUS_PLACEHOLDER": (()=>SLIDE_STATUS_PLACEHOLDER),
    "SLIDE_STATUS_PLAYING": (()=>SLIDE_STATUS_PLAYING),
    "UNKNOWN_ACTION_TYPE": (()=>UNKNOWN_ACTION_TYPE),
    "VK_ARROW_LEFT": (()=>VK_ARROW_LEFT),
    "VK_ARROW_RIGHT": (()=>VK_ARROW_RIGHT),
    "VK_ESCAPE": (()=>VK_ESCAPE),
    "activeSlideStatus": (()=>activeSlideStatus)
});
const MODULE_CAROUSEL = "carousel";
const MODULE_CONTROLLER = "controller";
const MODULE_NAVIGATION = "navigation";
const MODULE_NO_SCROLL = "no-scroll";
const MODULE_PORTAL = "portal";
const MODULE_ROOT = "root";
const MODULE_TOOLBAR = "toolbar";
const PLUGIN_CAPTIONS = "captions";
const PLUGIN_COUNTER = "counter";
const PLUGIN_DOWNLOAD = "download";
const PLUGIN_FULLSCREEN = "fullscreen";
const PLUGIN_INLINE = "inline";
const PLUGIN_SHARE = "share";
const PLUGIN_SLIDESHOW = "slideshow";
const PLUGIN_THUMBNAILS = "thumbnails";
const PLUGIN_ZOOM = "zoom";
const SLIDE_STATUS_LOADING = "loading";
const SLIDE_STATUS_PLAYING = "playing";
const SLIDE_STATUS_ERROR = "error";
const SLIDE_STATUS_COMPLETE = "complete";
const SLIDE_STATUS_PLACEHOLDER = "placeholder";
const activeSlideStatus = (status)=>`active-slide-${status}`;
const ACTIVE_SLIDE_LOADING = activeSlideStatus(SLIDE_STATUS_LOADING);
const ACTIVE_SLIDE_PLAYING = activeSlideStatus(SLIDE_STATUS_PLAYING);
const ACTIVE_SLIDE_ERROR = activeSlideStatus(SLIDE_STATUS_ERROR);
const ACTIVE_SLIDE_COMPLETE = activeSlideStatus(SLIDE_STATUS_COMPLETE);
const CLASS_FULLSIZE = "fullsize";
const CLASS_FLEX_CENTER = "flex_center";
const CLASS_NO_SCROLL = "no_scroll";
const CLASS_NO_SCROLL_PADDING = "no_scroll_padding";
const CLASS_SLIDE_WRAPPER = "slide_wrapper";
const CLASS_SLIDE_WRAPPER_INTERACTIVE = "slide_wrapper_interactive";
const ACTION_PREV = "prev";
const ACTION_NEXT = "next";
const ACTION_SWIPE = "swipe";
const ACTION_CLOSE = "close";
const EVENT_ON_POINTER_DOWN = "onPointerDown";
const EVENT_ON_POINTER_MOVE = "onPointerMove";
const EVENT_ON_POINTER_UP = "onPointerUp";
const EVENT_ON_POINTER_LEAVE = "onPointerLeave";
const EVENT_ON_POINTER_CANCEL = "onPointerCancel";
const EVENT_ON_KEY_DOWN = "onKeyDown";
const EVENT_ON_KEY_UP = "onKeyUp";
const EVENT_ON_WHEEL = "onWheel";
const VK_ESCAPE = "Escape";
const VK_ARROW_LEFT = "ArrowLeft";
const VK_ARROW_RIGHT = "ArrowRight";
const ELEMENT_BUTTON = "button";
const ELEMENT_ICON = "icon";
const IMAGE_FIT_CONTAIN = "contain";
const IMAGE_FIT_COVER = "cover";
const UNKNOWN_ACTION_TYPE = "Unknown action type";
;
}}),
"[project]/node_modules/yet-another-react-lightbox/dist/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Carousel": (()=>Carousel),
    "CarouselModule": (()=>CarouselModule),
    "CloseIcon": (()=>CloseIcon),
    "Controller": (()=>Controller),
    "ControllerContext": (()=>ControllerContext),
    "ControllerModule": (()=>ControllerModule),
    "DocumentContext": (()=>DocumentContext),
    "DocumentContextProvider": (()=>DocumentContextProvider),
    "ErrorIcon": (()=>ErrorIcon),
    "EventsContext": (()=>EventsContext),
    "EventsProvider": (()=>EventsProvider),
    "IconButton": (()=>IconButton),
    "ImageSlide": (()=>ImageSlide),
    "Lightbox": (()=>Lightbox),
    "LightboxDefaultProps": (()=>LightboxDefaultProps),
    "LightboxDispatchContext": (()=>LightboxDispatchContext),
    "LightboxPropsContext": (()=>LightboxPropsContext),
    "LightboxPropsProvider": (()=>LightboxPropsProvider),
    "LightboxRoot": (()=>LightboxRoot),
    "LightboxStateContext": (()=>LightboxStateContext),
    "LightboxStateProvider": (()=>LightboxStateProvider),
    "LoadingIcon": (()=>LoadingIcon),
    "Navigation": (()=>Navigation),
    "NavigationButton": (()=>NavigationButton),
    "NavigationModule": (()=>NavigationModule),
    "NextIcon": (()=>NextIcon),
    "NoScroll": (()=>NoScroll),
    "NoScrollModule": (()=>NoScrollModule),
    "Portal": (()=>Portal),
    "PortalModule": (()=>PortalModule),
    "PreviousIcon": (()=>PreviousIcon),
    "Root": (()=>Root),
    "RootModule": (()=>RootModule),
    "SwipeState": (()=>SwipeState),
    "TimeoutsContext": (()=>TimeoutsContext),
    "TimeoutsProvider": (()=>TimeoutsProvider),
    "Toolbar": (()=>Toolbar),
    "ToolbarModule": (()=>ToolbarModule),
    "addToolbarButton": (()=>addToolbarButton),
    "calculatePreload": (()=>calculatePreload),
    "cleanup": (()=>cleanup),
    "clsx": (()=>clsx),
    "composePrefix": (()=>composePrefix),
    "computeSlideRect": (()=>computeSlideRect),
    "createIcon": (()=>createIcon),
    "createIconDisabled": (()=>createIconDisabled),
    "createModule": (()=>createModule),
    "createNode": (()=>createNode),
    "cssClass": (()=>cssClass),
    "cssVar": (()=>cssVar),
    "default": (()=>Lightbox),
    "devicePixelRatio": (()=>devicePixelRatio),
    "getSlide": (()=>getSlide),
    "getSlideIfPresent": (()=>getSlideIfPresent),
    "getSlideIndex": (()=>getSlideIndex),
    "getSlideKey": (()=>getSlideKey),
    "hasSlides": (()=>hasSlides),
    "hasWindow": (()=>hasWindow),
    "isImageFitCover": (()=>isImageFitCover),
    "isImageSlide": (()=>isImageSlide),
    "label": (()=>label),
    "makeComposePrefix": (()=>makeComposePrefix),
    "makeInertWhen": (()=>makeInertWhen),
    "makeUseContext": (()=>makeUseContext),
    "parseInt": (()=>parseInt),
    "parseLengthPercentage": (()=>parseLengthPercentage),
    "round": (()=>round),
    "setRef": (()=>setRef),
    "stopNavigationEventsPropagation": (()=>stopNavigationEventsPropagation),
    "useAnimation": (()=>useAnimation),
    "useContainerRect": (()=>useContainerRect),
    "useController": (()=>useController),
    "useDelay": (()=>useDelay),
    "useDocumentContext": (()=>useDocumentContext),
    "useEventCallback": (()=>useEventCallback),
    "useEvents": (()=>useEvents),
    "useForkRef": (()=>useForkRef),
    "useKeyboardNavigation": (()=>useKeyboardNavigation),
    "useLayoutEffect": (()=>useLayoutEffect),
    "useLightboxDispatch": (()=>useLightboxDispatch),
    "useLightboxProps": (()=>useLightboxProps),
    "useLightboxState": (()=>useLightboxState),
    "useLoseFocus": (()=>useLoseFocus),
    "useMotionPreference": (()=>useMotionPreference),
    "useNavigationState": (()=>useNavigationState),
    "usePointerEvents": (()=>usePointerEvents),
    "usePointerSwipe": (()=>usePointerSwipe),
    "usePreventWheelDefaults": (()=>usePreventWheelDefaults),
    "useRTL": (()=>useRTL),
    "useSensors": (()=>useSensors),
    "useThrottle": (()=>useThrottle),
    "useTimeouts": (()=>useTimeouts),
    "useWheelSwipe": (()=>useWheelSwipe),
    "withPlugins": (()=>withPlugins)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/yet-another-react-lightbox/dist/types.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-dom/index.js [app-client] (ecmascript)");
'use client';
;
;
;
;
const cssPrefix$3 = "yarl__";
function clsx(...classes) {
    return [
        ...classes
    ].filter(Boolean).join(" ");
}
function cssClass(name) {
    return `${cssPrefix$3}${name}`;
}
function cssVar(name) {
    return `--${cssPrefix$3}${name}`;
}
function composePrefix(base, prefix) {
    return `${base}${prefix ? `_${prefix}` : ""}`;
}
function makeComposePrefix(base) {
    return (prefix)=>composePrefix(base, prefix);
}
function label(labels, defaultLabel) {
    var _a;
    return (_a = labels === null || labels === void 0 ? void 0 : labels[defaultLabel]) !== null && _a !== void 0 ? _a : defaultLabel;
}
function cleanup(...cleaners) {
    return ()=>{
        cleaners.forEach((cleaner)=>{
            cleaner();
        });
    };
}
function makeUseContext(name, contextName, context) {
    return ()=>{
        const ctx = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(context);
        if (!ctx) {
            throw new Error(`${name} must be used within a ${contextName}.Provider`);
        }
        return ctx;
    };
}
function hasWindow() {
    return typeof window !== "undefined";
}
function round(value, decimals = 0) {
    const factor = 10 ** decimals;
    return Math.round((value + Number.EPSILON) * factor) / factor;
}
function isImageSlide(slide) {
    return slide.type === undefined || slide.type === "image";
}
function isImageFitCover(image, imageFit) {
    return image.imageFit === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IMAGE_FIT_COVER"] || image.imageFit !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IMAGE_FIT_CONTAIN"] && imageFit === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IMAGE_FIT_COVER"];
}
function parseInt(value) {
    return typeof value === "string" ? Number.parseInt(value, 10) : value;
}
function parseLengthPercentage(input) {
    if (typeof input === "number") {
        return {
            pixel: input
        };
    }
    if (typeof input === "string") {
        const value = parseInt(input);
        return input.endsWith("%") ? {
            percent: value
        } : {
            pixel: value
        };
    }
    return {
        pixel: 0
    };
}
function computeSlideRect(containerRect, padding) {
    const paddingValue = parseLengthPercentage(padding);
    const paddingPixels = paddingValue.percent !== undefined ? containerRect.width / 100 * paddingValue.percent : paddingValue.pixel;
    return {
        width: Math.max(containerRect.width - 2 * paddingPixels, 0),
        height: Math.max(containerRect.height - 2 * paddingPixels, 0)
    };
}
function devicePixelRatio() {
    return (hasWindow() ? window === null || window === void 0 ? void 0 : window.devicePixelRatio : undefined) || 1;
}
function getSlideIndex(index, slidesCount) {
    return slidesCount > 0 ? (index % slidesCount + slidesCount) % slidesCount : 0;
}
function hasSlides(slides) {
    return slides.length > 0;
}
function getSlide(slides, index) {
    return slides[getSlideIndex(index, slides.length)];
}
function getSlideIfPresent(slides, index) {
    return hasSlides(slides) ? getSlide(slides, index) : undefined;
}
function getSlideKey(slide) {
    return isImageSlide(slide) ? slide.src : undefined;
}
function addToolbarButton(toolbar, key, button) {
    if (!button) return toolbar;
    const { buttons, ...restToolbar } = toolbar;
    const index = buttons.findIndex((item)=>item === key);
    const buttonWithKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(button) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cloneElement"])(button, {
        key
    }, null) : button;
    if (index >= 0) {
        const result = [
            ...buttons
        ];
        result.splice(index, 1, buttonWithKey);
        return {
            buttons: result,
            ...restToolbar
        };
    }
    return {
        buttons: [
            buttonWithKey,
            ...buttons
        ],
        ...restToolbar
    };
}
function stopNavigationEventsPropagation() {
    const stopPropagation = (event)=>{
        event.stopPropagation();
    };
    return {
        onPointerDown: stopPropagation,
        onKeyDown: stopPropagation,
        onWheel: stopPropagation
    };
}
function calculatePreload(carousel, slides, minimum = 0) {
    return Math.min(carousel.preload, Math.max(carousel.finite ? slides.length - 1 : Math.floor(slides.length / 2), minimum));
}
const isReact19 = Number(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["version"].split(".")[0]) >= 19;
function makeInertWhen(condition) {
    const legacyValue = condition ? "" : undefined;
    return {
        inert: isReact19 ? condition : legacyValue
    };
}
const LightboxDefaultProps = {
    open: false,
    close: ()=>{},
    index: 0,
    slides: [],
    render: {},
    plugins: [],
    toolbar: {
        buttons: [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_CLOSE"]
        ]
    },
    labels: {},
    animation: {
        fade: 250,
        swipe: 500,
        easing: {
            fade: "ease",
            swipe: "ease-out",
            navigation: "ease-in-out"
        }
    },
    carousel: {
        finite: false,
        preload: 2,
        padding: "16px",
        spacing: "30%",
        imageFit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IMAGE_FIT_CONTAIN"],
        imageProps: {}
    },
    controller: {
        ref: null,
        focus: true,
        aria: false,
        touchAction: "none",
        closeOnPullUp: false,
        closeOnPullDown: false,
        closeOnBackdropClick: false,
        preventDefaultWheelX: true,
        preventDefaultWheelY: false,
        disableSwipeNavigation: false
    },
    portal: {},
    noScroll: {
        disabled: false
    },
    on: {},
    styles: {},
    className: ""
};
function createModule(name, component) {
    return {
        name,
        component
    };
}
function createNode(module, children) {
    return {
        module,
        children
    };
}
function traverseNode(node, target, apply) {
    if (node.module.name === target) {
        return apply(node);
    }
    if (node.children) {
        return [
            createNode(node.module, node.children.flatMap((n)=>{
                var _a;
                return (_a = traverseNode(n, target, apply)) !== null && _a !== void 0 ? _a : [];
            }))
        ];
    }
    return [
        node
    ];
}
function traverse(nodes, target, apply) {
    return nodes.flatMap((node)=>{
        var _a;
        return (_a = traverseNode(node, target, apply)) !== null && _a !== void 0 ? _a : [];
    });
}
function withPlugins(root, plugins = [], augmentations = []) {
    let config = root;
    const contains = (target)=>{
        const nodes = [
            ...config
        ];
        while(nodes.length > 0){
            const node = nodes.pop();
            if ((node === null || node === void 0 ? void 0 : node.module.name) === target) return true;
            if (node === null || node === void 0 ? void 0 : node.children) nodes.push(...node.children);
        }
        return false;
    };
    const addParent = (target, module)=>{
        if (target === "") {
            config = [
                createNode(module, config)
            ];
            return;
        }
        config = traverse(config, target, (node)=>[
                createNode(module, [
                    node
                ])
            ]);
    };
    const append = (target, module)=>{
        config = traverse(config, target, (node)=>[
                createNode(node.module, [
                    createNode(module, node.children)
                ])
            ]);
    };
    const addChild = (target, module, precede)=>{
        config = traverse(config, target, (node)=>{
            var _a;
            return [
                createNode(node.module, [
                    ...precede ? [
                        createNode(module)
                    ] : [],
                    ...(_a = node.children) !== null && _a !== void 0 ? _a : [],
                    ...!precede ? [
                        createNode(module)
                    ] : []
                ])
            ];
        });
    };
    const addSibling = (target, module, precede)=>{
        config = traverse(config, target, (node)=>[
                ...precede ? [
                    createNode(module)
                ] : [],
                node,
                ...!precede ? [
                    createNode(module)
                ] : []
            ]);
    };
    const addModule = (module)=>{
        append(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_CONTROLLER"], module);
    };
    const replace = (target, module)=>{
        config = traverse(config, target, (node)=>[
                createNode(module, node.children)
            ]);
    };
    const remove = (target)=>{
        config = traverse(config, target, (node)=>node.children);
    };
    const augment = (augmentation)=>{
        augmentations.push(augmentation);
    };
    plugins.forEach((plugin)=>{
        plugin({
            contains,
            addParent,
            append,
            addChild,
            addSibling,
            addModule,
            replace,
            remove,
            augment
        });
    });
    return {
        config,
        augmentation: (props)=>augmentations.reduce((acc, augmentation)=>augmentation(acc), props)
    };
}
const DocumentContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const useDocumentContext = makeUseContext("useDocument", "DocumentContext", DocumentContext);
function DocumentContextProvider({ nodeRef, children }) {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "DocumentContextProvider.useMemo[context]": ()=>{
            const getOwnerDocument = {
                "DocumentContextProvider.useMemo[context].getOwnerDocument": (node)=>{
                    var _a;
                    return ((_a = node || nodeRef.current) === null || _a === void 0 ? void 0 : _a.ownerDocument) || document;
                }
            }["DocumentContextProvider.useMemo[context].getOwnerDocument"];
            const getOwnerWindow = {
                "DocumentContextProvider.useMemo[context].getOwnerWindow": (node)=>{
                    var _a;
                    return ((_a = getOwnerDocument(node)) === null || _a === void 0 ? void 0 : _a.defaultView) || window;
                }
            }["DocumentContextProvider.useMemo[context].getOwnerWindow"];
            return {
                getOwnerDocument,
                getOwnerWindow
            };
        }
    }["DocumentContextProvider.useMemo[context]"], [
        nodeRef
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(DocumentContext.Provider, {
        value: context
    }, children);
}
const EventsContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const useEvents = makeUseContext("useEvents", "EventsContext", EventsContext);
function EventsProvider({ children }) {
    const [subscriptions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "EventsProvider.useEffect": ()=>({
                "EventsProvider.useEffect": ()=>{
                    Object.keys(subscriptions).forEach({
                        "EventsProvider.useEffect": (topic)=>delete subscriptions[topic]
                    }["EventsProvider.useEffect"]);
                }
            })["EventsProvider.useEffect"]
    }["EventsProvider.useEffect"], [
        subscriptions
    ]);
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "EventsProvider.useMemo[context]": ()=>{
            const unsubscribe = {
                "EventsProvider.useMemo[context].unsubscribe": (topic, callback)=>{
                    var _a;
                    (_a = subscriptions[topic]) === null || _a === void 0 ? void 0 : _a.splice(0, subscriptions[topic].length, ...subscriptions[topic].filter({
                        "EventsProvider.useMemo[context].unsubscribe": (cb)=>cb !== callback
                    }["EventsProvider.useMemo[context].unsubscribe"]));
                }
            }["EventsProvider.useMemo[context].unsubscribe"];
            const subscribe = {
                "EventsProvider.useMemo[context].subscribe": (topic, callback)=>{
                    if (!subscriptions[topic]) {
                        subscriptions[topic] = [];
                    }
                    subscriptions[topic].push(callback);
                    return ({
                        "EventsProvider.useMemo[context].subscribe": ()=>unsubscribe(topic, callback)
                    })["EventsProvider.useMemo[context].subscribe"];
                }
            }["EventsProvider.useMemo[context].subscribe"];
            const publish = {
                "EventsProvider.useMemo[context].publish": (...[topic, event])=>{
                    var _a;
                    (_a = subscriptions[topic]) === null || _a === void 0 ? void 0 : _a.forEach({
                        "EventsProvider.useMemo[context].publish": (callback)=>callback(event)
                    }["EventsProvider.useMemo[context].publish"]);
                }
            }["EventsProvider.useMemo[context].publish"];
            return {
                publish,
                subscribe,
                unsubscribe
            };
        }
    }["EventsProvider.useMemo[context]"], [
        subscriptions
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(EventsContext.Provider, {
        value: context
    }, children);
}
const LightboxPropsContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const useLightboxProps = makeUseContext("useLightboxProps", "LightboxPropsContext", LightboxPropsContext);
function LightboxPropsProvider({ children, ...props }) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(LightboxPropsContext.Provider, {
        value: props
    }, children);
}
const LightboxStateContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const useLightboxState = makeUseContext("useLightboxState", "LightboxStateContext", LightboxStateContext);
const LightboxDispatchContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const useLightboxDispatch = makeUseContext("useLightboxDispatch", "LightboxDispatchContext", LightboxDispatchContext);
function reducer(state, action) {
    switch(action.type){
        case "swipe":
            {
                const { slides } = state;
                const increment = (action === null || action === void 0 ? void 0 : action.increment) || 0;
                const globalIndex = state.globalIndex + increment;
                const currentIndex = getSlideIndex(globalIndex, slides.length);
                const currentSlide = getSlideIfPresent(slides, currentIndex);
                const animation = increment || action.duration ? {
                    increment,
                    duration: action.duration,
                    easing: action.easing
                } : undefined;
                return {
                    slides,
                    currentIndex,
                    globalIndex,
                    currentSlide,
                    animation
                };
            }
        case "update":
            if (action.slides !== state.slides || action.index !== state.currentIndex) {
                return {
                    slides: action.slides,
                    currentIndex: action.index,
                    globalIndex: action.index,
                    currentSlide: getSlideIfPresent(action.slides, action.index)
                };
            }
            return state;
        default:
            throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UNKNOWN_ACTION_TYPE"]);
    }
}
function LightboxStateProvider({ slides, index, children }) {
    const [state, dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReducer"])(reducer, {
        slides,
        currentIndex: index,
        globalIndex: index,
        currentSlide: getSlideIfPresent(slides, index)
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LightboxStateProvider.useEffect": ()=>{
            dispatch({
                type: "update",
                slides,
                index
            });
        }
    }["LightboxStateProvider.useEffect"], [
        slides,
        index
    ]);
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "LightboxStateProvider.useMemo[context]": ()=>({
                ...state,
                state,
                dispatch
            })
    }["LightboxStateProvider.useMemo[context]"], [
        state,
        dispatch
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(LightboxDispatchContext.Provider, {
        value: dispatch
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(LightboxStateContext.Provider, {
        value: context
    }, children));
}
const TimeoutsContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const useTimeouts = makeUseContext("useTimeouts", "TimeoutsContext", TimeoutsContext);
function TimeoutsProvider({ children }) {
    const [timeouts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TimeoutsProvider.useEffect": ()=>({
                "TimeoutsProvider.useEffect": ()=>{
                    timeouts.forEach({
                        "TimeoutsProvider.useEffect": (tid)=>window.clearTimeout(tid)
                    }["TimeoutsProvider.useEffect"]);
                    timeouts.splice(0, timeouts.length);
                }
            })["TimeoutsProvider.useEffect"]
    }["TimeoutsProvider.useEffect"], [
        timeouts
    ]);
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "TimeoutsProvider.useMemo[context]": ()=>{
            const removeTimeout = {
                "TimeoutsProvider.useMemo[context].removeTimeout": (id)=>{
                    timeouts.splice(0, timeouts.length, ...timeouts.filter({
                        "TimeoutsProvider.useMemo[context].removeTimeout": (tid)=>tid !== id
                    }["TimeoutsProvider.useMemo[context].removeTimeout"]));
                }
            }["TimeoutsProvider.useMemo[context].removeTimeout"];
            const setTimeout = {
                "TimeoutsProvider.useMemo[context].setTimeout": (fn, delay)=>{
                    const id = window.setTimeout({
                        "TimeoutsProvider.useMemo[context].setTimeout.id": ()=>{
                            removeTimeout(id);
                            fn();
                        }
                    }["TimeoutsProvider.useMemo[context].setTimeout.id"], delay);
                    timeouts.push(id);
                    return id;
                }
            }["TimeoutsProvider.useMemo[context].setTimeout"];
            const clearTimeout = {
                "TimeoutsProvider.useMemo[context].clearTimeout": (id)=>{
                    if (id !== undefined) {
                        removeTimeout(id);
                        window.clearTimeout(id);
                    }
                }
            }["TimeoutsProvider.useMemo[context].clearTimeout"];
            return {
                setTimeout,
                clearTimeout
            };
        }
    }["TimeoutsProvider.useMemo[context]"], [
        timeouts
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(TimeoutsContext.Provider, {
        value: context
    }, children);
}
const IconButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(function IconButton({ label: label$1, className, icon: Icon, renderIcon, onClick, style, ...rest }, ref) {
    const { styles, labels } = useLightboxProps();
    const buttonLabel = label(labels, label$1);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("button", {
        ref: ref,
        type: "button",
        title: buttonLabel,
        "aria-label": buttonLabel,
        className: clsx(cssClass(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ELEMENT_BUTTON"]), className),
        onClick: onClick,
        style: {
            ...style,
            ...styles.button
        },
        ...rest
    }, renderIcon ? renderIcon() : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(Icon, {
        className: cssClass(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ELEMENT_ICON"]),
        style: styles.icon
    }));
});
function svgIcon(name, children) {
    const icon = (props)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("svg", {
            xmlns: "http://www.w3.org/2000/svg",
            viewBox: "0 0 24 24",
            width: "24",
            height: "24",
            "aria-hidden": "true",
            focusable: "false",
            ...props
        }, children);
    icon.displayName = name;
    return icon;
}
function createIcon(name, glyph) {
    return svgIcon(name, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("g", {
        fill: "currentColor"
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M0 0h24v24H0z",
        fill: "none"
    }), glyph));
}
function createIconDisabled(name, glyph) {
    return svgIcon(name, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("defs", null, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("mask", {
        id: "strike"
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M0 0h24v24H0z",
        fill: "white"
    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M0 0L24 24",
        stroke: "black",
        strokeWidth: 4
    }))), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M0.70707 2.121320L21.878680 23.292883",
        stroke: "currentColor",
        strokeWidth: 2
    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("g", {
        fill: "currentColor",
        mask: "url(#strike)"
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M0 0h24v24H0z",
        fill: "none"
    }), glyph)));
}
const CloseIcon = createIcon("Close", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
    d: "M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
}));
const PreviousIcon = createIcon("Previous", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
    d: "M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"
}));
const NextIcon = createIcon("Next", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
    d: "M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"
}));
const LoadingIcon = createIcon("Loading", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, Array.from({
    length: 8
}).map((_, index, array)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("line", {
        key: index,
        x1: "12",
        y1: "6.5",
        x2: "12",
        y2: "1.8",
        strokeLinecap: "round",
        strokeWidth: "2.6",
        stroke: "currentColor",
        strokeOpacity: 1 / array.length * (index + 1),
        transform: `rotate(${360 / array.length * index}, 12, 12)`
    }))));
const ErrorIcon = createIcon("Error", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
    d: "M21.9,21.9l-8.49-8.49l0,0L3.59,3.59l0,0L2.1,2.1L0.69,3.51L3,5.83V19c0,1.1,0.9,2,2,2h13.17l2.31,2.31L21.9,21.9z M5,18 l3.5-4.5l2.5,3.01L12.17,15l3,3H5z M21,18.17L5.83,3H19c1.1,0,2,0.9,2,2V18.17z"
}));
const useLayoutEffect = hasWindow() ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"];
function useMotionPreference() {
    const [reduceMotion, setReduceMotion] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useMotionPreference.useEffect": ()=>{
            var _a, _b;
            const mediaQuery = (_a = window.matchMedia) === null || _a === void 0 ? void 0 : _a.call(window, "(prefers-reduced-motion: reduce)");
            setReduceMotion(mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.matches);
            const listener = {
                "useMotionPreference.useEffect.listener": (event)=>setReduceMotion(event.matches)
            }["useMotionPreference.useEffect.listener"];
            (_b = mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.addEventListener) === null || _b === void 0 ? void 0 : _b.call(mediaQuery, "change", listener);
            return ({
                "useMotionPreference.useEffect": ()=>{
                    var _a;
                    return (_a = mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.removeEventListener) === null || _a === void 0 ? void 0 : _a.call(mediaQuery, "change", listener);
                }
            })["useMotionPreference.useEffect"];
        }
    }["useMotionPreference.useEffect"], []);
    return reduceMotion;
}
function currentTransformation(node) {
    let x = 0;
    let y = 0;
    let z = 0;
    const matrix = window.getComputedStyle(node).transform;
    const matcher = matrix.match(/matrix.*\((.+)\)/);
    if (matcher) {
        const values = matcher[1].split(",").map(parseInt);
        if (values.length === 6) {
            x = values[4];
            y = values[5];
        } else if (values.length === 16) {
            x = values[12];
            y = values[13];
            z = values[14];
        }
    }
    return {
        x,
        y,
        z
    };
}
function useAnimation(nodeRef, computeAnimation) {
    const snapshot = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const animation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const reduceMotion = useMotionPreference();
    useLayoutEffect({
        "useAnimation.useLayoutEffect": ()=>{
            var _a, _b, _c;
            if (nodeRef.current && snapshot.current !== undefined && !reduceMotion) {
                const { keyframes, duration, easing, onfinish } = computeAnimation(snapshot.current, nodeRef.current.getBoundingClientRect(), currentTransformation(nodeRef.current)) || {};
                if (keyframes && duration) {
                    (_a = animation.current) === null || _a === void 0 ? void 0 : _a.cancel();
                    animation.current = undefined;
                    try {
                        animation.current = (_c = (_b = nodeRef.current).animate) === null || _c === void 0 ? void 0 : _c.call(_b, keyframes, {
                            duration,
                            easing
                        });
                    } catch (err) {
                        console.error(err);
                    }
                    if (animation.current) {
                        animation.current.onfinish = ({
                            "useAnimation.useLayoutEffect": ()=>{
                                animation.current = undefined;
                                onfinish === null || onfinish === void 0 ? void 0 : onfinish();
                            }
                        })["useAnimation.useLayoutEffect"];
                    }
                }
            }
            snapshot.current = undefined;
        }
    }["useAnimation.useLayoutEffect"]);
    return {
        prepareAnimation: (currentSnapshot)=>{
            snapshot.current = currentSnapshot;
        },
        isAnimationPlaying: ()=>{
            var _a;
            return ((_a = animation.current) === null || _a === void 0 ? void 0 : _a.playState) === "running";
        }
    };
}
function useContainerRect() {
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const observerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const [containerRect, setContainerRect] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    const setContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContainerRect.useCallback[setContainerRef]": (node)=>{
            containerRef.current = node;
            if (observerRef.current) {
                observerRef.current.disconnect();
                observerRef.current = undefined;
            }
            const updateContainerRect = {
                "useContainerRect.useCallback[setContainerRef].updateContainerRect": ()=>{
                    if (node) {
                        const styles = window.getComputedStyle(node);
                        const parse = {
                            "useContainerRect.useCallback[setContainerRef].updateContainerRect.parse": (value)=>parseFloat(value) || 0
                        }["useContainerRect.useCallback[setContainerRef].updateContainerRect.parse"];
                        setContainerRect({
                            width: Math.round(node.clientWidth - parse(styles.paddingLeft) - parse(styles.paddingRight)),
                            height: Math.round(node.clientHeight - parse(styles.paddingTop) - parse(styles.paddingBottom))
                        });
                    } else {
                        setContainerRect(undefined);
                    }
                }
            }["useContainerRect.useCallback[setContainerRef].updateContainerRect"];
            updateContainerRect();
            if (node && typeof ResizeObserver !== "undefined") {
                observerRef.current = new ResizeObserver(updateContainerRect);
                observerRef.current.observe(node);
            }
        }
    }["useContainerRect.useCallback[setContainerRef]"], []);
    return {
        setContainerRef,
        containerRef,
        containerRect
    };
}
function useDelay() {
    const timeoutId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const { setTimeout, clearTimeout } = useTimeouts();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useDelay.useCallback": (callback, delay)=>{
            clearTimeout(timeoutId.current);
            timeoutId.current = setTimeout(callback, delay > 0 ? delay : 0);
        }
    }["useDelay.useCallback"], [
        setTimeout,
        clearTimeout
    ]);
}
function useEventCallback(fn) {
    const ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(fn);
    useLayoutEffect({
        "useEventCallback.useLayoutEffect": ()=>{
            ref.current = fn;
        }
    }["useEventCallback.useLayoutEffect"]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useEventCallback.useCallback": (...args)=>{
            var _a;
            return (_a = ref.current) === null || _a === void 0 ? void 0 : _a.call(ref, ...args);
        }
    }["useEventCallback.useCallback"], []);
}
function setRef(ref, value) {
    if (typeof ref === "function") {
        ref(value);
    } else if (ref) {
        ref.current = value;
    }
}
function useForkRef(refA, refB) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useForkRef.useMemo": ()=>refA == null && refB == null ? null : ({
                "useForkRef.useMemo": (refValue)=>{
                    setRef(refA, refValue);
                    setRef(refB, refValue);
                }
            })["useForkRef.useMemo"]
    }["useForkRef.useMemo"], [
        refA,
        refB
    ]);
}
function useLoseFocus(focus, disabled = false) {
    const focused = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    useLayoutEffect({
        "useLoseFocus.useLayoutEffect": ()=>{
            if (disabled && focused.current) {
                focused.current = false;
                focus();
            }
        }
    }["useLoseFocus.useLayoutEffect"], [
        disabled,
        focus
    ]);
    const onFocus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLoseFocus.useCallback[onFocus]": ()=>{
            focused.current = true;
        }
    }["useLoseFocus.useCallback[onFocus]"], []);
    const onBlur = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLoseFocus.useCallback[onBlur]": ()=>{
            focused.current = false;
        }
    }["useLoseFocus.useCallback[onBlur]"], []);
    return {
        onFocus,
        onBlur
    };
}
function useRTL() {
    const [isRTL, setIsRTL] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    useLayoutEffect({
        "useRTL.useLayoutEffect": ()=>{
            setIsRTL(window.getComputedStyle(window.document.documentElement).direction === "rtl");
        }
    }["useRTL.useLayoutEffect"], []);
    return isRTL;
}
function useSensors() {
    const [subscribers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const notifySubscribers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSensors.useCallback[notifySubscribers]": (type, event)=>{
            var _a;
            (_a = subscribers[type]) === null || _a === void 0 ? void 0 : _a.forEach({
                "useSensors.useCallback[notifySubscribers]": (listener)=>{
                    if (!event.isPropagationStopped()) listener(event);
                }
            }["useSensors.useCallback[notifySubscribers]"]);
        }
    }["useSensors.useCallback[notifySubscribers]"], [
        subscribers
    ]);
    const registerSensors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useSensors.useMemo[registerSensors]": ()=>({
                onPointerDown: ({
                    "useSensors.useMemo[registerSensors]": (event)=>notifySubscribers(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_POINTER_DOWN"], event)
                })["useSensors.useMemo[registerSensors]"],
                onPointerMove: ({
                    "useSensors.useMemo[registerSensors]": (event)=>notifySubscribers(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_POINTER_MOVE"], event)
                })["useSensors.useMemo[registerSensors]"],
                onPointerUp: ({
                    "useSensors.useMemo[registerSensors]": (event)=>notifySubscribers(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_POINTER_UP"], event)
                })["useSensors.useMemo[registerSensors]"],
                onPointerLeave: ({
                    "useSensors.useMemo[registerSensors]": (event)=>notifySubscribers(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_POINTER_LEAVE"], event)
                })["useSensors.useMemo[registerSensors]"],
                onPointerCancel: ({
                    "useSensors.useMemo[registerSensors]": (event)=>notifySubscribers(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_POINTER_CANCEL"], event)
                })["useSensors.useMemo[registerSensors]"],
                onKeyDown: ({
                    "useSensors.useMemo[registerSensors]": (event)=>notifySubscribers(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_KEY_DOWN"], event)
                })["useSensors.useMemo[registerSensors]"],
                onKeyUp: ({
                    "useSensors.useMemo[registerSensors]": (event)=>notifySubscribers(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_KEY_UP"], event)
                })["useSensors.useMemo[registerSensors]"],
                onWheel: ({
                    "useSensors.useMemo[registerSensors]": (event)=>notifySubscribers(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_WHEEL"], event)
                })["useSensors.useMemo[registerSensors]"]
            })
    }["useSensors.useMemo[registerSensors]"], [
        notifySubscribers
    ]);
    const subscribeSensors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSensors.useCallback[subscribeSensors]": (type, callback)=>{
            if (!subscribers[type]) {
                subscribers[type] = [];
            }
            subscribers[type].unshift(callback);
            return ({
                "useSensors.useCallback[subscribeSensors]": ()=>{
                    const listeners = subscribers[type];
                    if (listeners) {
                        listeners.splice(0, listeners.length, ...listeners.filter({
                            "useSensors.useCallback[subscribeSensors]": (el)=>el !== callback
                        }["useSensors.useCallback[subscribeSensors]"]));
                    }
                }
            })["useSensors.useCallback[subscribeSensors]"];
        }
    }["useSensors.useCallback[subscribeSensors]"], [
        subscribers
    ]);
    return {
        registerSensors,
        subscribeSensors
    };
}
function useThrottle(callback, delay) {
    const lastCallbackTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const delayCallback = useDelay();
    const executeCallback = useEventCallback({
        "useThrottle.useEventCallback[executeCallback]": (...args)=>{
            lastCallbackTime.current = Date.now();
            callback(args);
        }
    }["useThrottle.useEventCallback[executeCallback]"]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useThrottle.useCallback": (...args)=>{
            delayCallback({
                "useThrottle.useCallback": ()=>{
                    executeCallback(args);
                }
            }["useThrottle.useCallback"], delay - (Date.now() - lastCallbackTime.current));
        }
    }["useThrottle.useCallback"], [
        delay,
        executeCallback,
        delayCallback
    ]);
}
const slidePrefix = makeComposePrefix("slide");
const slideImagePrefix = makeComposePrefix("slide_image");
function ImageSlide({ slide: image, offset, render, rect, imageFit, imageProps, onClick, onLoad, onError, style }) {
    var _a, _b, _c, _d, _e, _f, _g;
    const [status, setStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_LOADING"]);
    const { publish } = useEvents();
    const { setTimeout } = useTimeouts();
    const imageRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ImageSlide.useEffect": ()=>{
            if (offset === 0) {
                publish((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["activeSlideStatus"])(status));
            }
        }
    }["ImageSlide.useEffect"], [
        offset,
        status,
        publish
    ]);
    const handleLoading = useEventCallback({
        "ImageSlide.useEventCallback[handleLoading]": (img)=>{
            ("decode" in img ? img.decode() : Promise.resolve()).catch({
                "ImageSlide.useEventCallback[handleLoading]": ()=>{}
            }["ImageSlide.useEventCallback[handleLoading]"]).then({
                "ImageSlide.useEventCallback[handleLoading]": ()=>{
                    if (!img.parentNode) {
                        return;
                    }
                    setStatus(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_COMPLETE"]);
                    setTimeout({
                        "ImageSlide.useEventCallback[handleLoading]": ()=>{
                            onLoad === null || onLoad === void 0 ? void 0 : onLoad(img);
                        }
                    }["ImageSlide.useEventCallback[handleLoading]"], 0);
                }
            }["ImageSlide.useEventCallback[handleLoading]"]);
        }
    }["ImageSlide.useEventCallback[handleLoading]"]);
    const setImageRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ImageSlide.useCallback[setImageRef]": (img)=>{
            imageRef.current = img;
            if (img === null || img === void 0 ? void 0 : img.complete) {
                handleLoading(img);
            }
        }
    }["ImageSlide.useCallback[setImageRef]"], [
        handleLoading
    ]);
    const handleOnLoad = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ImageSlide.useCallback[handleOnLoad]": (event)=>{
            handleLoading(event.currentTarget);
        }
    }["ImageSlide.useCallback[handleOnLoad]"], [
        handleLoading
    ]);
    const handleOnError = useEventCallback({
        "ImageSlide.useEventCallback[handleOnError]": ()=>{
            setStatus(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_ERROR"]);
            onError === null || onError === void 0 ? void 0 : onError();
        }
    }["ImageSlide.useEventCallback[handleOnError]"]);
    const cover = isImageFitCover(image, imageFit);
    const nonInfinite = (value, fallback)=>Number.isFinite(value) ? value : fallback;
    const maxWidth = nonInfinite(Math.max(...((_b = (_a = image.srcSet) === null || _a === void 0 ? void 0 : _a.map((x)=>x.width)) !== null && _b !== void 0 ? _b : []).concat(image.width ? [
        image.width
    ] : []).filter(Boolean)), ((_c = imageRef.current) === null || _c === void 0 ? void 0 : _c.naturalWidth) || 0);
    const maxHeight = nonInfinite(Math.max(...((_e = (_d = image.srcSet) === null || _d === void 0 ? void 0 : _d.map((x)=>x.height)) !== null && _e !== void 0 ? _e : []).concat(image.height ? [
        image.height
    ] : []).filter(Boolean)), ((_f = imageRef.current) === null || _f === void 0 ? void 0 : _f.naturalHeight) || 0);
    const defaultStyle = maxWidth && maxHeight ? {
        maxWidth: `min(${maxWidth}px, 100%)`,
        maxHeight: `min(${maxHeight}px, 100%)`
    } : {
        maxWidth: "100%",
        maxHeight: "100%"
    };
    const srcSet = (_g = image.srcSet) === null || _g === void 0 ? void 0 : _g.sort((a, b)=>a.width - b.width).map((item)=>`${item.src} ${item.width}w`).join(", ");
    const estimateActualWidth = ()=>rect && !cover && image.width && image.height ? rect.height / image.height * image.width : Number.MAX_VALUE;
    const sizes = srcSet && rect && hasWindow() ? `${Math.round(Math.min(estimateActualWidth(), rect.width))}px` : undefined;
    const { style: imagePropsStyle, className: imagePropsClassName, ...restImageProps } = imageProps || {};
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("img", {
        ref: setImageRef,
        onLoad: handleOnLoad,
        onError: handleOnError,
        onClick: onClick,
        draggable: false,
        className: clsx(cssClass(slideImagePrefix()), cover && cssClass(slideImagePrefix("cover")), status !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_COMPLETE"] && cssClass(slideImagePrefix("loading")), imagePropsClassName),
        style: {
            ...defaultStyle,
            ...style,
            ...imagePropsStyle
        },
        ...restImageProps,
        alt: image.alt,
        sizes: sizes,
        srcSet: srcSet,
        src: image.src
    }), status !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_COMPLETE"] && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: cssClass(slidePrefix(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_PLACEHOLDER"]))
    }, status === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_LOADING"] && ((render === null || render === void 0 ? void 0 : render.iconLoading) ? render.iconLoading() : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(LoadingIcon, {
        className: clsx(cssClass(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ELEMENT_ICON"]), cssClass(slidePrefix(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_LOADING"])))
    })), status === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_ERROR"] && ((render === null || render === void 0 ? void 0 : render.iconError) ? render.iconError() : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(ErrorIcon, {
        className: clsx(cssClass(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ELEMENT_ICON"]), cssClass(slidePrefix(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_ERROR"])))
    }))));
}
const LightboxRoot = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(function LightboxRoot({ className, children, ...rest }, ref) {
    const nodeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(DocumentContextProvider, {
        nodeRef: nodeRef
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        ref: useForkRef(ref, nodeRef),
        className: clsx(cssClass("root"), className),
        ...rest
    }, children));
});
var SwipeState;
(function(SwipeState) {
    SwipeState[SwipeState["NONE"] = 0] = "NONE";
    SwipeState[SwipeState["SWIPE"] = 1] = "SWIPE";
    SwipeState[SwipeState["PULL"] = 2] = "PULL";
    SwipeState[SwipeState["ANIMATION"] = 3] = "ANIMATION";
})(SwipeState || (SwipeState = {}));
function usePointerEvents(subscribeSensors, onPointerDown, onPointerMove, onPointerUp, disabled) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "usePointerEvents.useEffect": ()=>!disabled ? cleanup(subscribeSensors(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_POINTER_DOWN"], onPointerDown), subscribeSensors(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_POINTER_MOVE"], onPointerMove), subscribeSensors(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_POINTER_UP"], onPointerUp), subscribeSensors(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_POINTER_LEAVE"], onPointerUp), subscribeSensors(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_POINTER_CANCEL"], onPointerUp)) : ({
                "usePointerEvents.useEffect": ()=>{}
            })["usePointerEvents.useEffect"]
    }["usePointerEvents.useEffect"], [
        subscribeSensors,
        onPointerDown,
        onPointerMove,
        onPointerUp,
        disabled
    ]);
}
var Gesture;
(function(Gesture) {
    Gesture[Gesture["NONE"] = 0] = "NONE";
    Gesture[Gesture["SWIPE"] = 1] = "SWIPE";
    Gesture[Gesture["PULL"] = 2] = "PULL";
})(Gesture || (Gesture = {}));
const SWIPE_THRESHOLD = 30;
function usePointerSwipe({ disableSwipeNavigation }, subscribeSensors, isSwipeValid, containerWidth, swipeAnimationDuration, onSwipeStart, onSwipeProgress, onSwipeFinish, onSwipeCancel, pullUpEnabled, pullDownEnabled, onPullStart, onPullProgress, onPullFinish, onPullCancel) {
    const offset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const pointers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    const activePointer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const startTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const gesture = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(Gesture.NONE);
    const clearPointer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePointerSwipe.useCallback[clearPointer]": (event)=>{
            if (activePointer.current === event.pointerId) {
                activePointer.current = undefined;
                gesture.current = Gesture.NONE;
            }
            const currentPointers = pointers.current;
            currentPointers.splice(0, currentPointers.length, ...currentPointers.filter({
                "usePointerSwipe.useCallback[clearPointer]": (p)=>p.pointerId !== event.pointerId
            }["usePointerSwipe.useCallback[clearPointer]"]));
        }
    }["usePointerSwipe.useCallback[clearPointer]"], []);
    const addPointer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePointerSwipe.useCallback[addPointer]": (event)=>{
            clearPointer(event);
            event.persist();
            pointers.current.push(event);
        }
    }["usePointerSwipe.useCallback[addPointer]"], [
        clearPointer
    ]);
    const onPointerDown = useEventCallback({
        "usePointerSwipe.useEventCallback[onPointerDown]": (event)=>{
            addPointer(event);
        }
    }["usePointerSwipe.useEventCallback[onPointerDown]"]);
    const exceedsPullThreshold = (value, threshold)=>pullDownEnabled && value > threshold || pullUpEnabled && value < -threshold;
    const onPointerUp = useEventCallback({
        "usePointerSwipe.useEventCallback[onPointerUp]": (event)=>{
            if (pointers.current.find({
                "usePointerSwipe.useEventCallback[onPointerUp]": (x)=>x.pointerId === event.pointerId
            }["usePointerSwipe.useEventCallback[onPointerUp]"]) && activePointer.current === event.pointerId) {
                const duration = Date.now() - startTime.current;
                const currentOffset = offset.current;
                if (gesture.current === Gesture.SWIPE) {
                    if (Math.abs(currentOffset) > 0.3 * containerWidth || Math.abs(currentOffset) > 5 && duration < swipeAnimationDuration) {
                        onSwipeFinish(currentOffset, duration);
                    } else {
                        onSwipeCancel(currentOffset);
                    }
                } else if (gesture.current === Gesture.PULL) {
                    if (exceedsPullThreshold(currentOffset, 2 * SWIPE_THRESHOLD)) {
                        onPullFinish(currentOffset, duration);
                    } else {
                        onPullCancel(currentOffset);
                    }
                }
                offset.current = 0;
                gesture.current = Gesture.NONE;
            }
            clearPointer(event);
        }
    }["usePointerSwipe.useEventCallback[onPointerUp]"]);
    const onPointerMove = useEventCallback({
        "usePointerSwipe.useEventCallback[onPointerMove]": (event)=>{
            const pointer = pointers.current.find({
                "usePointerSwipe.useEventCallback[onPointerMove].pointer": (p)=>p.pointerId === event.pointerId
            }["usePointerSwipe.useEventCallback[onPointerMove].pointer"]);
            if (pointer) {
                const isCurrentPointer = activePointer.current === event.pointerId;
                if (event.buttons === 0) {
                    if (isCurrentPointer && offset.current !== 0) {
                        onPointerUp(event);
                    } else {
                        clearPointer(pointer);
                    }
                    return;
                }
                const deltaX = event.clientX - pointer.clientX;
                const deltaY = event.clientY - pointer.clientY;
                if (activePointer.current === undefined) {
                    const startGesture = {
                        "usePointerSwipe.useEventCallback[onPointerMove].startGesture": (newGesture)=>{
                            addPointer(event);
                            activePointer.current = event.pointerId;
                            startTime.current = Date.now();
                            gesture.current = newGesture;
                        }
                    }["usePointerSwipe.useEventCallback[onPointerMove].startGesture"];
                    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > SWIPE_THRESHOLD && isSwipeValid(deltaX)) {
                        if (!disableSwipeNavigation) {
                            startGesture(Gesture.SWIPE);
                            onSwipeStart();
                        }
                    } else if (Math.abs(deltaY) > Math.abs(deltaX) && exceedsPullThreshold(deltaY, SWIPE_THRESHOLD)) {
                        startGesture(Gesture.PULL);
                        onPullStart();
                    }
                } else if (isCurrentPointer) {
                    if (gesture.current === Gesture.SWIPE) {
                        offset.current = deltaX;
                        onSwipeProgress(deltaX);
                    } else if (gesture.current === Gesture.PULL) {
                        offset.current = deltaY;
                        onPullProgress(deltaY);
                    }
                }
            }
        }
    }["usePointerSwipe.useEventCallback[onPointerMove]"]);
    usePointerEvents(subscribeSensors, onPointerDown, onPointerMove, onPointerUp);
}
function usePreventWheelDefaults({ preventDefaultWheelX, preventDefaultWheelY }) {
    const ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const listener = useEventCallback({
        "usePreventWheelDefaults.useEventCallback[listener]": (event)=>{
            const horizontal = Math.abs(event.deltaX) > Math.abs(event.deltaY);
            if (horizontal && preventDefaultWheelX || !horizontal && preventDefaultWheelY || event.ctrlKey) {
                event.preventDefault();
            }
        }
    }["usePreventWheelDefaults.useEventCallback[listener]"]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePreventWheelDefaults.useCallback": (node)=>{
            var _a;
            if (node) {
                node.addEventListener("wheel", listener, {
                    passive: false
                });
            } else {
                (_a = ref.current) === null || _a === void 0 ? void 0 : _a.removeEventListener("wheel", listener);
            }
            ref.current = node;
        }
    }["usePreventWheelDefaults.useCallback"], [
        listener
    ]);
}
function useWheelSwipe(swipeState, subscribeSensors, isSwipeValid, containerWidth, swipeAnimationDuration, onSwipeStart, onSwipeProgress, onSwipeFinish, onSwipeCancel) {
    const offset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const intent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const intentCleanup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const resetCleanup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const wheelInertia = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const wheelInertiaCleanup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const startTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const { setTimeout, clearTimeout } = useTimeouts();
    const cancelSwipeIntentCleanup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useWheelSwipe.useCallback[cancelSwipeIntentCleanup]": ()=>{
            if (intentCleanup.current) {
                clearTimeout(intentCleanup.current);
                intentCleanup.current = undefined;
            }
        }
    }["useWheelSwipe.useCallback[cancelSwipeIntentCleanup]"], [
        clearTimeout
    ]);
    const cancelSwipeResetCleanup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useWheelSwipe.useCallback[cancelSwipeResetCleanup]": ()=>{
            if (resetCleanup.current) {
                clearTimeout(resetCleanup.current);
                resetCleanup.current = undefined;
            }
        }
    }["useWheelSwipe.useCallback[cancelSwipeResetCleanup]"], [
        clearTimeout
    ]);
    const handleCleanup = useEventCallback({
        "useWheelSwipe.useEventCallback[handleCleanup]": ()=>{
            if (swipeState !== SwipeState.SWIPE) {
                offset.current = 0;
                startTime.current = 0;
                cancelSwipeIntentCleanup();
                cancelSwipeResetCleanup();
            }
        }
    }["useWheelSwipe.useEventCallback[handleCleanup]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(handleCleanup, [
        swipeState,
        handleCleanup
    ]);
    const handleCancelSwipe = useEventCallback({
        "useWheelSwipe.useEventCallback[handleCancelSwipe]": (currentSwipeOffset)=>{
            resetCleanup.current = undefined;
            if (offset.current === currentSwipeOffset) {
                onSwipeCancel(offset.current);
            }
        }
    }["useWheelSwipe.useEventCallback[handleCancelSwipe]"]);
    const onWheel = useEventCallback({
        "useWheelSwipe.useEventCallback[onWheel]": (event)=>{
            if (event.ctrlKey) {
                return;
            }
            if (Math.abs(event.deltaY) > Math.abs(event.deltaX)) {
                return;
            }
            const setWheelInertia = {
                "useWheelSwipe.useEventCallback[onWheel].setWheelInertia": (inertia)=>{
                    wheelInertia.current = inertia;
                    clearTimeout(wheelInertiaCleanup.current);
                    wheelInertiaCleanup.current = inertia > 0 ? setTimeout({
                        "useWheelSwipe.useEventCallback[onWheel].setWheelInertia": ()=>{
                            wheelInertia.current = 0;
                            wheelInertiaCleanup.current = undefined;
                        }
                    }["useWheelSwipe.useEventCallback[onWheel].setWheelInertia"], 300) : undefined;
                }
            }["useWheelSwipe.useEventCallback[onWheel].setWheelInertia"];
            if (swipeState === SwipeState.NONE) {
                if (Math.abs(event.deltaX) <= 1.2 * Math.abs(wheelInertia.current)) {
                    setWheelInertia(event.deltaX);
                    return;
                }
                if (!isSwipeValid(-event.deltaX)) {
                    return;
                }
                intent.current += event.deltaX;
                cancelSwipeIntentCleanup();
                if (Math.abs(intent.current) > 30) {
                    intent.current = 0;
                    setWheelInertia(0);
                    startTime.current = Date.now();
                    onSwipeStart();
                } else {
                    const currentSwipeIntent = intent.current;
                    intentCleanup.current = setTimeout({
                        "useWheelSwipe.useEventCallback[onWheel]": ()=>{
                            intentCleanup.current = undefined;
                            if (currentSwipeIntent === intent.current) {
                                intent.current = 0;
                            }
                        }
                    }["useWheelSwipe.useEventCallback[onWheel]"], swipeAnimationDuration);
                }
            } else if (swipeState === SwipeState.SWIPE) {
                let newSwipeOffset = offset.current - event.deltaX;
                newSwipeOffset = Math.min(Math.abs(newSwipeOffset), containerWidth) * Math.sign(newSwipeOffset);
                offset.current = newSwipeOffset;
                onSwipeProgress(newSwipeOffset);
                cancelSwipeResetCleanup();
                if (Math.abs(newSwipeOffset) > 0.2 * containerWidth) {
                    setWheelInertia(event.deltaX);
                    onSwipeFinish(newSwipeOffset, Date.now() - startTime.current);
                    return;
                }
                resetCleanup.current = setTimeout({
                    "useWheelSwipe.useEventCallback[onWheel]": ()=>handleCancelSwipe(newSwipeOffset)
                }["useWheelSwipe.useEventCallback[onWheel]"], 2 * swipeAnimationDuration);
            } else {
                setWheelInertia(event.deltaX);
            }
        }
    }["useWheelSwipe.useEventCallback[onWheel]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useWheelSwipe.useEffect": ()=>subscribeSensors(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_WHEEL"], onWheel)
    }["useWheelSwipe.useEffect"], [
        subscribeSensors,
        onWheel
    ]);
}
const cssContainerPrefix = makeComposePrefix("container");
const ControllerContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const useController = makeUseContext("useController", "ControllerContext", ControllerContext);
function Controller({ children, ...props }) {
    var _a;
    const { carousel, animation, controller, on, styles, render } = props;
    const { closeOnPullUp, closeOnPullDown, preventDefaultWheelX, preventDefaultWheelY } = controller;
    const [toolbarWidth, setToolbarWidth] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    const state = useLightboxState();
    const dispatch = useLightboxDispatch();
    const [swipeState, setSwipeState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(SwipeState.NONE);
    const swipeOffset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const pullOffset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const pullOpacity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(1);
    const { registerSensors, subscribeSensors } = useSensors();
    const { subscribe, publish } = useEvents();
    const cleanupAnimationIncrement = useDelay();
    const cleanupSwipeOffset = useDelay();
    const cleanupPullOffset = useDelay();
    const { containerRef, setContainerRef, containerRect } = useContainerRect();
    const handleContainerRef = useForkRef(usePreventWheelDefaults({
        preventDefaultWheelX,
        preventDefaultWheelY
    }), setContainerRef);
    const carouselRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const setCarouselRef = useForkRef(carouselRef, undefined);
    const { getOwnerDocument } = useDocumentContext();
    const isRTL = useRTL();
    const rtl = (value)=>(isRTL ? -1 : 1) * (typeof value === "number" ? value : 1);
    const focus = useEventCallback({
        "Controller.useEventCallback[focus]": ()=>{
            var _a;
            return (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.focus();
        }
    }["Controller.useEventCallback[focus]"]);
    const getLightboxProps = useEventCallback({
        "Controller.useEventCallback[getLightboxProps]": ()=>props
    }["Controller.useEventCallback[getLightboxProps]"]);
    const getLightboxState = useEventCallback({
        "Controller.useEventCallback[getLightboxState]": ()=>state
    }["Controller.useEventCallback[getLightboxState]"]);
    const prev = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Controller.useCallback[prev]": (params)=>publish(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_PREV"], params)
    }["Controller.useCallback[prev]"], [
        publish
    ]);
    const next = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Controller.useCallback[next]": (params)=>publish(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_NEXT"], params)
    }["Controller.useCallback[next]"], [
        publish
    ]);
    const close = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Controller.useCallback[close]": ()=>publish(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_CLOSE"])
    }["Controller.useCallback[close]"], [
        publish
    ]);
    const isSwipeValid = (offset)=>!(carousel.finite && (rtl(offset) > 0 && state.currentIndex === 0 || rtl(offset) < 0 && state.currentIndex === state.slides.length - 1));
    const setSwipeOffset = (offset)=>{
        var _a;
        swipeOffset.current = offset;
        (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.style.setProperty(cssVar("swipe_offset"), `${Math.round(offset)}px`);
    };
    const setPullOffset = (offset)=>{
        var _a, _b;
        pullOffset.current = offset;
        pullOpacity.current = (()=>{
            const threshold = 60;
            const minOpacity = 0.5;
            const offsetValue = (()=>{
                if (closeOnPullDown && offset > 0) return offset;
                if (closeOnPullUp && offset < 0) return -offset;
                return 0;
            })();
            return Math.min(Math.max(round(1 - offsetValue / threshold * (1 - minOpacity), 2), minOpacity), 1);
        })();
        (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.style.setProperty(cssVar("pull_offset"), `${Math.round(offset)}px`);
        (_b = containerRef.current) === null || _b === void 0 ? void 0 : _b.style.setProperty(cssVar("pull_opacity"), `${pullOpacity.current}`);
    };
    const { prepareAnimation: preparePullAnimation } = useAnimation(carouselRef, {
        "Controller.useAnimation": (snapshot, rect, translate)=>{
            if (carouselRef.current && containerRect) {
                return {
                    keyframes: [
                        {
                            transform: `translate(0, ${snapshot.rect.y - rect.y + translate.y}px)`,
                            opacity: snapshot.opacity
                        },
                        {
                            transform: "translate(0, 0)",
                            opacity: 1
                        }
                    ],
                    duration: snapshot.duration,
                    easing: animation.easing.fade
                };
            }
            return undefined;
        }
    }["Controller.useAnimation"]);
    const pull = (offset, cancel)=>{
        if (closeOnPullUp || closeOnPullDown) {
            setPullOffset(offset);
            let duration = 0;
            if (carouselRef.current) {
                duration = animation.fade * (cancel ? 2 : 1);
                preparePullAnimation({
                    rect: carouselRef.current.getBoundingClientRect(),
                    opacity: pullOpacity.current,
                    duration
                });
            }
            cleanupPullOffset(()=>{
                setPullOffset(0);
                setSwipeState(SwipeState.NONE);
            }, duration);
            setSwipeState(SwipeState.ANIMATION);
            if (!cancel) {
                close();
            }
        }
    };
    const { prepareAnimation, isAnimationPlaying } = useAnimation(carouselRef, {
        "Controller.useAnimation": (snapshot, rect, translate)=>{
            var _a;
            if (carouselRef.current && containerRect && ((_a = state.animation) === null || _a === void 0 ? void 0 : _a.duration)) {
                const parsedSpacing = parseLengthPercentage(carousel.spacing);
                const spacingValue = (parsedSpacing.percent ? parsedSpacing.percent * containerRect.width / 100 : parsedSpacing.pixel) || 0;
                return {
                    keyframes: [
                        {
                            transform: `translate(${rtl(state.globalIndex - snapshot.index) * (containerRect.width + spacingValue) + snapshot.rect.x - rect.x + translate.x}px, 0)`
                        },
                        {
                            transform: "translate(0, 0)"
                        }
                    ],
                    duration: state.animation.duration,
                    easing: state.animation.easing
                };
            }
            return undefined;
        }
    }["Controller.useAnimation"]);
    const swipe = useEventCallback({
        "Controller.useEventCallback[swipe]": (action)=>{
            var _a, _b;
            const currentSwipeOffset = action.offset || 0;
            const swipeDuration = !currentSwipeOffset ? (_a = animation.navigation) !== null && _a !== void 0 ? _a : animation.swipe : animation.swipe;
            const swipeEasing = !currentSwipeOffset && !isAnimationPlaying() ? animation.easing.navigation : animation.easing.swipe;
            let { direction } = action;
            const count = (_b = action.count) !== null && _b !== void 0 ? _b : 1;
            let newSwipeState = SwipeState.ANIMATION;
            let newSwipeAnimationDuration = swipeDuration * count;
            if (!direction) {
                const containerWidth = containerRect === null || containerRect === void 0 ? void 0 : containerRect.width;
                const elapsedTime = action.duration || 0;
                const expectedTime = containerWidth ? swipeDuration / containerWidth * Math.abs(currentSwipeOffset) : swipeDuration;
                if (count !== 0) {
                    if (elapsedTime < expectedTime) {
                        newSwipeAnimationDuration = newSwipeAnimationDuration / expectedTime * Math.max(elapsedTime, expectedTime / 5);
                    } else if (containerWidth) {
                        newSwipeAnimationDuration = swipeDuration / containerWidth * (containerWidth - Math.abs(currentSwipeOffset));
                    }
                    direction = rtl(currentSwipeOffset) > 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_PREV"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_NEXT"];
                } else {
                    newSwipeAnimationDuration = swipeDuration / 2;
                }
            }
            let increment = 0;
            if (direction === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_PREV"]) {
                if (isSwipeValid(rtl(1))) {
                    increment = -count;
                } else {
                    newSwipeState = SwipeState.NONE;
                    newSwipeAnimationDuration = swipeDuration;
                }
            } else if (direction === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_NEXT"]) {
                if (isSwipeValid(rtl(-1))) {
                    increment = count;
                } else {
                    newSwipeState = SwipeState.NONE;
                    newSwipeAnimationDuration = swipeDuration;
                }
            }
            newSwipeAnimationDuration = Math.round(newSwipeAnimationDuration);
            cleanupSwipeOffset({
                "Controller.useEventCallback[swipe]": ()=>{
                    setSwipeOffset(0);
                    setSwipeState(SwipeState.NONE);
                }
            }["Controller.useEventCallback[swipe]"], newSwipeAnimationDuration);
            if (carouselRef.current) {
                prepareAnimation({
                    rect: carouselRef.current.getBoundingClientRect(),
                    index: state.globalIndex
                });
            }
            setSwipeState(newSwipeState);
            publish(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_SWIPE"], {
                type: "swipe",
                increment,
                duration: newSwipeAnimationDuration,
                easing: swipeEasing
            });
        }
    }["Controller.useEventCallback[swipe]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Controller.useEffect": ()=>{
            var _a, _b;
            if (((_a = state.animation) === null || _a === void 0 ? void 0 : _a.increment) && ((_b = state.animation) === null || _b === void 0 ? void 0 : _b.duration)) {
                cleanupAnimationIncrement({
                    "Controller.useEffect": ()=>dispatch({
                            type: "swipe",
                            increment: 0
                        })
                }["Controller.useEffect"], state.animation.duration);
            }
        }
    }["Controller.useEffect"], [
        state.animation,
        dispatch,
        cleanupAnimationIncrement
    ]);
    const swipeParams = [
        subscribeSensors,
        isSwipeValid,
        (containerRect === null || containerRect === void 0 ? void 0 : containerRect.width) || 0,
        animation.swipe,
        ()=>setSwipeState(SwipeState.SWIPE),
        (offset)=>setSwipeOffset(offset),
        (offset, duration)=>swipe({
                offset,
                duration,
                count: 1
            }),
        (offset)=>swipe({
                offset,
                count: 0
            })
    ];
    const pullParams = [
        ()=>{
            if (closeOnPullDown) {
                setSwipeState(SwipeState.PULL);
            }
        },
        (offset)=>setPullOffset(offset),
        (offset)=>pull(offset),
        (offset)=>pull(offset, true)
    ];
    usePointerSwipe(controller, ...swipeParams, closeOnPullUp, closeOnPullDown, ...pullParams);
    useWheelSwipe(swipeState, ...swipeParams);
    const focusOnMount = useEventCallback({
        "Controller.useEventCallback[focusOnMount]": ()=>{
            if (controller.focus && getOwnerDocument().querySelector(`.${cssClass(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_PORTAL"])} .${cssClass(cssContainerPrefix())}`)) {
                focus();
            }
        }
    }["Controller.useEventCallback[focusOnMount]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(focusOnMount, [
        focusOnMount
    ]);
    const onViewCallback = useEventCallback({
        "Controller.useEventCallback[onViewCallback]": ()=>{
            var _a;
            (_a = on.view) === null || _a === void 0 ? void 0 : _a.call(on, {
                index: state.currentIndex
            });
        }
    }["Controller.useEventCallback[onViewCallback]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(onViewCallback, [
        state.globalIndex,
        onViewCallback
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Controller.useEffect": ()=>cleanup(subscribe(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_PREV"], {
                "Controller.useEffect": (action)=>swipe({
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_PREV"],
                        ...action
                    })
            }["Controller.useEffect"]), subscribe(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_NEXT"], {
                "Controller.useEffect": (action)=>swipe({
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_NEXT"],
                        ...action
                    })
            }["Controller.useEffect"]), subscribe(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_SWIPE"], {
                "Controller.useEffect": (action)=>dispatch(action)
            }["Controller.useEffect"]))
    }["Controller.useEffect"], [
        subscribe,
        swipe,
        dispatch
    ]);
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Controller.useMemo[context]": ()=>({
                prev,
                next,
                close,
                focus,
                slideRect: containerRect ? computeSlideRect(containerRect, carousel.padding) : {
                    width: 0,
                    height: 0
                },
                containerRect: containerRect || {
                    width: 0,
                    height: 0
                },
                subscribeSensors,
                containerRef,
                setCarouselRef,
                toolbarWidth,
                setToolbarWidth
            })
    }["Controller.useMemo[context]"], [
        prev,
        next,
        close,
        focus,
        subscribeSensors,
        containerRect,
        containerRef,
        setCarouselRef,
        toolbarWidth,
        setToolbarWidth,
        carousel.padding
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(controller.ref, {
        "Controller.useImperativeHandle": ()=>({
                prev,
                next,
                close,
                focus,
                getLightboxProps,
                getLightboxState
            })
    }["Controller.useImperativeHandle"], [
        prev,
        next,
        close,
        focus,
        getLightboxProps,
        getLightboxState
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        ref: handleContainerRef,
        className: clsx(cssClass(cssContainerPrefix()), cssClass(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLASS_FLEX_CENTER"])),
        style: {
            ...swipeState === SwipeState.SWIPE ? {
                [cssVar("swipe_offset")]: `${Math.round(swipeOffset.current)}px`
            } : null,
            ...swipeState === SwipeState.PULL ? {
                [cssVar("pull_offset")]: `${Math.round(pullOffset.current)}px`,
                [cssVar("pull_opacity")]: `${pullOpacity.current}`
            } : null,
            ...controller.touchAction !== "none" ? {
                [cssVar("controller_touch_action")]: controller.touchAction
            } : null,
            ...styles.container
        },
        ...controller.aria ? {
            role: "presentation",
            "aria-live": "polite"
        } : null,
        tabIndex: -1,
        ...registerSensors
    }, containerRect && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(ControllerContext.Provider, {
        value: context
    }, children, (_a = render.controls) === null || _a === void 0 ? void 0 : _a.call(render)));
}
const ControllerModule = createModule(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_CONTROLLER"], Controller);
function cssPrefix$2(value) {
    return composePrefix(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_CAROUSEL"], value);
}
function cssSlidePrefix(value) {
    return composePrefix("slide", value);
}
function CarouselSlide({ slide, offset }) {
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const { currentIndex } = useLightboxState();
    const { slideRect, close, focus } = useController();
    const { render, carousel: { imageFit, imageProps }, on: { click: onClick }, controller: { closeOnBackdropClick }, styles: { slide: style } } = useLightboxProps();
    const { getOwnerDocument } = useDocumentContext();
    const offscreen = offset !== 0;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CarouselSlide.useEffect": ()=>{
            var _a;
            if (offscreen && ((_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.contains(getOwnerDocument().activeElement))) {
                focus();
            }
        }
    }["CarouselSlide.useEffect"], [
        offscreen,
        focus,
        getOwnerDocument
    ]);
    const renderSlide = ()=>{
        var _a, _b, _c, _d;
        let rendered = (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, {
            slide,
            offset,
            rect: slideRect
        });
        if (!rendered && isImageSlide(slide)) {
            rendered = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(ImageSlide, {
                slide: slide,
                offset: offset,
                render: render,
                rect: slideRect,
                imageFit: imageFit,
                imageProps: imageProps,
                onClick: !offscreen ? ()=>onClick === null || onClick === void 0 ? void 0 : onClick({
                        index: currentIndex
                    }) : undefined
            });
        }
        return rendered ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, (_b = render.slideHeader) === null || _b === void 0 ? void 0 : _b.call(render, {
            slide
        }), ((_c = render.slideContainer) !== null && _c !== void 0 ? _c : ({ children })=>children)({
            slide,
            children: rendered
        }), (_d = render.slideFooter) === null || _d === void 0 ? void 0 : _d.call(render, {
            slide
        })) : null;
    };
    const handleBackdropClick = (event)=>{
        const container = containerRef.current;
        const target = event.target instanceof HTMLElement ? event.target : undefined;
        if (closeOnBackdropClick && target && container && (target === container || Array.from(container.children).find((x)=>x === target) && target.classList.contains(cssClass(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLASS_SLIDE_WRAPPER"])))) {
            close();
        }
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        ref: containerRef,
        className: clsx(cssClass(cssSlidePrefix()), !offscreen && cssClass(cssSlidePrefix("current")), cssClass(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLASS_FLEX_CENTER"])),
        ...makeInertWhen(offscreen),
        onClick: handleBackdropClick,
        style: style
    }, renderSlide());
}
function Placeholder() {
    const style = useLightboxProps().styles.slide;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: cssClass("slide"),
        style: style
    });
}
function Carousel({ carousel }) {
    const { slides, currentIndex, globalIndex } = useLightboxState();
    const { setCarouselRef } = useController();
    const spacingValue = parseLengthPercentage(carousel.spacing);
    const paddingValue = parseLengthPercentage(carousel.padding);
    const preload = calculatePreload(carousel, slides, 1);
    const items = [];
    if (hasSlides(slides)) {
        for(let index = currentIndex - preload; index <= currentIndex + preload; index += 1){
            const slide = getSlide(slides, index);
            const key = globalIndex - currentIndex + index;
            const placeholder = carousel.finite && (index < 0 || index > slides.length - 1);
            items.push(!placeholder ? {
                key: [
                    `${key}`,
                    getSlideKey(slide)
                ].filter(Boolean).join("|"),
                offset: index - currentIndex,
                slide
            } : {
                key
            });
        }
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        ref: setCarouselRef,
        className: clsx(cssClass(cssPrefix$2()), items.length > 0 && cssClass(cssPrefix$2("with_slides"))),
        style: {
            [`${cssVar(cssPrefix$2("slides_count"))}`]: items.length,
            [`${cssVar(cssPrefix$2("spacing_px"))}`]: spacingValue.pixel || 0,
            [`${cssVar(cssPrefix$2("spacing_percent"))}`]: spacingValue.percent || 0,
            [`${cssVar(cssPrefix$2("padding_px"))}`]: paddingValue.pixel || 0,
            [`${cssVar(cssPrefix$2("padding_percent"))}`]: paddingValue.percent || 0
        }
    }, items.map(({ key, slide, offset })=>slide ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(CarouselSlide, {
            key: key,
            slide: slide,
            offset: offset
        }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(Placeholder, {
            key: key
        })));
}
const CarouselModule = createModule(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_CAROUSEL"], Carousel);
function useNavigationState() {
    const { carousel } = useLightboxProps();
    const { slides, currentIndex } = useLightboxState();
    const prevDisabled = slides.length === 0 || carousel.finite && currentIndex === 0;
    const nextDisabled = slides.length === 0 || carousel.finite && currentIndex === slides.length - 1;
    return {
        prevDisabled,
        nextDisabled
    };
}
function useKeyboardNavigation(subscribeSensors) {
    var _a;
    const isRTL = useRTL();
    const { publish } = useEvents();
    const { animation } = useLightboxProps();
    const { prevDisabled, nextDisabled } = useNavigationState();
    const throttle = ((_a = animation.navigation) !== null && _a !== void 0 ? _a : animation.swipe) / 2;
    const prev = useThrottle({
        "useKeyboardNavigation.useThrottle[prev]": ()=>publish(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_PREV"])
    }["useKeyboardNavigation.useThrottle[prev]"], throttle);
    const next = useThrottle({
        "useKeyboardNavigation.useThrottle[next]": ()=>publish(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_NEXT"])
    }["useKeyboardNavigation.useThrottle[next]"], throttle);
    const handleKeyDown = useEventCallback({
        "useKeyboardNavigation.useEventCallback[handleKeyDown]": (event)=>{
            switch(event.key){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VK_ESCAPE"]:
                    publish(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_CLOSE"]);
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VK_ARROW_LEFT"]:
                    if (!(isRTL ? nextDisabled : prevDisabled)) (isRTL ? next : prev)();
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VK_ARROW_RIGHT"]:
                    if (!(isRTL ? prevDisabled : nextDisabled)) (isRTL ? prev : next)();
                    break;
                default:
            }
        }
    }["useKeyboardNavigation.useEventCallback[handleKeyDown]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useKeyboardNavigation.useEffect": ()=>subscribeSensors(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_KEY_DOWN"], handleKeyDown)
    }["useKeyboardNavigation.useEffect"], [
        subscribeSensors,
        handleKeyDown
    ]);
}
function NavigationButton({ label, icon, renderIcon, action, onClick, disabled, style }) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(IconButton, {
        label: label,
        icon: icon,
        renderIcon: renderIcon,
        className: cssClass(`navigation_${action}`),
        disabled: disabled,
        onClick: onClick,
        style: style,
        ...useLoseFocus(useController().focus, disabled)
    });
}
function Navigation({ render: { buttonPrev, buttonNext, iconPrev, iconNext }, styles }) {
    const { prev, next, subscribeSensors } = useController();
    const { prevDisabled, nextDisabled } = useNavigationState();
    useKeyboardNavigation(subscribeSensors);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, buttonPrev ? buttonPrev() : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(NavigationButton, {
        label: "Previous",
        action: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_PREV"],
        icon: PreviousIcon,
        renderIcon: iconPrev,
        style: styles.navigationPrev,
        disabled: prevDisabled,
        onClick: prev
    }), buttonNext ? buttonNext() : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(NavigationButton, {
        label: "Next",
        action: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_NEXT"],
        icon: NextIcon,
        renderIcon: iconNext,
        style: styles.navigationNext,
        disabled: nextDisabled,
        onClick: next
    }));
}
const NavigationModule = createModule(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_NAVIGATION"], Navigation);
const noScroll = cssClass(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLASS_NO_SCROLL"]);
const noScrollPadding = cssClass(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLASS_NO_SCROLL_PADDING"]);
function isHTMLElement(element) {
    return "style" in element;
}
function padScrollbar(element, padding, rtl) {
    const styles = window.getComputedStyle(element);
    const property = rtl ? "padding-left" : "padding-right";
    const computedValue = rtl ? styles.paddingLeft : styles.paddingRight;
    const originalValue = element.style.getPropertyValue(property);
    element.style.setProperty(property, `${(parseInt(computedValue) || 0) + padding}px`);
    return ()=>{
        if (originalValue) {
            element.style.setProperty(property, originalValue);
        } else {
            element.style.removeProperty(property);
        }
    };
}
function NoScroll({ noScroll: { disabled }, children }) {
    const rtl = useRTL();
    const { getOwnerDocument, getOwnerWindow } = useDocumentContext();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NoScroll.useEffect": ()=>{
            if (disabled) return ({
                "NoScroll.useEffect": ()=>{}
            })["NoScroll.useEffect"];
            const cleanup = [];
            const ownerWindow = getOwnerWindow();
            const { body, documentElement } = getOwnerDocument();
            const scrollbar = Math.round(ownerWindow.innerWidth - documentElement.clientWidth);
            if (scrollbar > 0) {
                cleanup.push(padScrollbar(body, scrollbar, rtl));
                const elements = body.getElementsByTagName("*");
                for(let i = 0; i < elements.length; i += 1){
                    const element = elements[i];
                    if (isHTMLElement(element) && ownerWindow.getComputedStyle(element).getPropertyValue("position") === "fixed" && !element.classList.contains(noScrollPadding)) {
                        cleanup.push(padScrollbar(element, scrollbar, rtl));
                    }
                }
            }
            body.classList.add(noScroll);
            return ({
                "NoScroll.useEffect": ()=>{
                    body.classList.remove(noScroll);
                    cleanup.forEach({
                        "NoScroll.useEffect": (clean)=>clean()
                    }["NoScroll.useEffect"]);
                }
            })["NoScroll.useEffect"];
        }
    }["NoScroll.useEffect"], [
        rtl,
        disabled,
        getOwnerDocument,
        getOwnerWindow
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, children);
}
const NoScrollModule = createModule(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_NO_SCROLL"], NoScroll);
function cssPrefix$1(value) {
    return composePrefix(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_PORTAL"], value);
}
function setAttribute(element, attribute, value) {
    const previousValue = element.getAttribute(attribute);
    element.setAttribute(attribute, value);
    return ()=>{
        if (previousValue) {
            element.setAttribute(attribute, previousValue);
        } else {
            element.removeAttribute(attribute);
        }
    };
}
function Portal({ children, animation, styles, className, on, portal, close }) {
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [visible, setVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const cleanup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    const restoreFocus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const { setTimeout } = useTimeouts();
    const { subscribe } = useEvents();
    const reduceMotion = useMotionPreference();
    const animationDuration = !reduceMotion ? animation.fade : 0;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Portal.useEffect": ()=>{
            setMounted(true);
            return ({
                "Portal.useEffect": ()=>{
                    setMounted(false);
                    setVisible(false);
                }
            })["Portal.useEffect"];
        }
    }["Portal.useEffect"], []);
    const handleCleanup = useEventCallback({
        "Portal.useEventCallback[handleCleanup]": ()=>{
            cleanup.current.forEach({
                "Portal.useEventCallback[handleCleanup]": (clean)=>clean()
            }["Portal.useEventCallback[handleCleanup]"]);
            cleanup.current = [];
        }
    }["Portal.useEventCallback[handleCleanup]"]);
    const handleClose = useEventCallback({
        "Portal.useEventCallback[handleClose]": ()=>{
            var _a;
            setVisible(false);
            handleCleanup();
            (_a = on.exiting) === null || _a === void 0 ? void 0 : _a.call(on);
            setTimeout({
                "Portal.useEventCallback[handleClose]": ()=>{
                    var _a;
                    (_a = on.exited) === null || _a === void 0 ? void 0 : _a.call(on);
                    close();
                }
            }["Portal.useEventCallback[handleClose]"], animationDuration);
        }
    }["Portal.useEventCallback[handleClose]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Portal.useEffect": ()=>subscribe(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_CLOSE"], handleClose)
    }["Portal.useEffect"], [
        subscribe,
        handleClose
    ]);
    const handleEnter = useEventCallback({
        "Portal.useEventCallback[handleEnter]": (node)=>{
            var _a, _b, _c;
            node.scrollTop;
            setVisible(true);
            (_a = on.entering) === null || _a === void 0 ? void 0 : _a.call(on);
            const elements = (_c = (_b = node.parentNode) === null || _b === void 0 ? void 0 : _b.children) !== null && _c !== void 0 ? _c : [];
            for(let i = 0; i < elements.length; i += 1){
                const element = elements[i];
                if ([
                    "TEMPLATE",
                    "SCRIPT",
                    "STYLE"
                ].indexOf(element.tagName) === -1 && element !== node) {
                    cleanup.current.push(setAttribute(element, "inert", ""));
                    cleanup.current.push(setAttribute(element, "aria-hidden", "true"));
                }
            }
            cleanup.current.push({
                "Portal.useEventCallback[handleEnter]": ()=>{
                    var _a, _b;
                    (_b = (_a = restoreFocus.current) === null || _a === void 0 ? void 0 : _a.focus) === null || _b === void 0 ? void 0 : _b.call(_a);
                }
            }["Portal.useEventCallback[handleEnter]"]);
            setTimeout({
                "Portal.useEventCallback[handleEnter]": ()=>{
                    var _a;
                    (_a = on.entered) === null || _a === void 0 ? void 0 : _a.call(on);
                }
            }["Portal.useEventCallback[handleEnter]"], animationDuration);
        }
    }["Portal.useEventCallback[handleEnter]"]);
    const handleRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Portal.useCallback[handleRef]": (node)=>{
            if (node) {
                handleEnter(node);
            } else {
                handleCleanup();
            }
        }
    }["Portal.useCallback[handleRef]"], [
        handleEnter,
        handleCleanup
    ]);
    return mounted ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPortal"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(LightboxRoot, {
        ref: handleRef,
        className: clsx(className, cssClass(cssPrefix$1()), cssClass(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLASS_NO_SCROLL_PADDING"]), visible && cssClass(cssPrefix$1("open"))),
        role: "presentation",
        "aria-live": "polite",
        style: {
            ...animation.fade !== LightboxDefaultProps.animation.fade ? {
                [cssVar("fade_animation_duration")]: `${animationDuration}ms`
            } : null,
            ...animation.easing.fade !== LightboxDefaultProps.animation.easing.fade ? {
                [cssVar("fade_animation_timing_function")]: animation.easing.fade
            } : null,
            ...styles.root
        },
        onFocus: (event)=>{
            if (!restoreFocus.current) {
                restoreFocus.current = event.relatedTarget;
            }
        }
    }, children), portal.root || document.body) : null;
}
const PortalModule = createModule(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_PORTAL"], Portal);
function Root({ children }) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, children);
}
const RootModule = createModule(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_ROOT"], Root);
function cssPrefix(value) {
    return composePrefix(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_TOOLBAR"], value);
}
function Toolbar({ toolbar: { buttons }, render: { buttonClose, iconClose }, styles }) {
    const { close, setToolbarWidth } = useController();
    const { setContainerRef, containerRect } = useContainerRect();
    useLayoutEffect({
        "Toolbar.useLayoutEffect": ()=>{
            setToolbarWidth(containerRect === null || containerRect === void 0 ? void 0 : containerRect.width);
        }
    }["Toolbar.useLayoutEffect"], [
        setToolbarWidth,
        containerRect === null || containerRect === void 0 ? void 0 : containerRect.width
    ]);
    const renderCloseButton = ()=>{
        if (buttonClose) return buttonClose();
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(IconButton, {
            key: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_CLOSE"],
            label: "Close",
            icon: CloseIcon,
            renderIcon: iconClose,
            onClick: close
        });
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        ref: setContainerRef,
        style: styles.toolbar,
        className: cssClass(cssPrefix())
    }, buttons === null || buttons === void 0 ? void 0 : buttons.map((button)=>button === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTION_CLOSE"] ? renderCloseButton() : button));
}
const ToolbarModule = createModule(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_TOOLBAR"], Toolbar);
function renderNode(node, props) {
    var _a;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(node.module.component, {
        key: node.module.name,
        ...props
    }, (_a = node.children) === null || _a === void 0 ? void 0 : _a.map((child)=>renderNode(child, props)));
}
function mergeAnimation(defaultAnimation, animation = {}) {
    const { easing: defaultAnimationEasing, ...restDefaultAnimation } = defaultAnimation;
    const { easing, ...restAnimation } = animation;
    return {
        easing: {
            ...defaultAnimationEasing,
            ...easing
        },
        ...restDefaultAnimation,
        ...restAnimation
    };
}
function Lightbox({ carousel, animation, render, toolbar, controller, noScroll, on, plugins, slides, index, ...restProps }) {
    const { animation: defaultAnimation, carousel: defaultCarousel, render: defaultRender, toolbar: defaultToolbar, controller: defaultController, noScroll: defaultNoScroll, on: defaultOn, slides: defaultSlides, index: defaultIndex, plugins: defaultPlugins, ...restDefaultProps } = LightboxDefaultProps;
    const { config, augmentation } = withPlugins([
        createNode(PortalModule, [
            createNode(NoScrollModule, [
                createNode(ControllerModule, [
                    createNode(CarouselModule),
                    createNode(ToolbarModule),
                    createNode(NavigationModule)
                ])
            ])
        ])
    ], plugins || defaultPlugins);
    const props = augmentation({
        animation: mergeAnimation(defaultAnimation, animation),
        carousel: {
            ...defaultCarousel,
            ...carousel
        },
        render: {
            ...defaultRender,
            ...render
        },
        toolbar: {
            ...defaultToolbar,
            ...toolbar
        },
        controller: {
            ...defaultController,
            ...controller
        },
        noScroll: {
            ...defaultNoScroll,
            ...noScroll
        },
        on: {
            ...defaultOn,
            ...on
        },
        ...restDefaultProps,
        ...restProps
    });
    if (!props.open) return null;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(LightboxPropsProvider, {
        ...props
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(LightboxStateProvider, {
        slides: slides || defaultSlides,
        index: parseInt(index || defaultIndex)
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(TimeoutsProvider, null, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(EventsProvider, null, renderNode(createNode(RootModule, config), props)))));
}
;
}}),
"[project]/node_modules/yet-another-react-lightbox/dist/plugins/fullscreen/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Fullscreen)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/yet-another-react-lightbox/dist/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/yet-another-react-lightbox/dist/types.js [app-client] (ecmascript)");
;
;
;
const defaultFullscreenProps = {
    auto: false,
    ref: null
};
const resolveFullscreenProps = (fullscreen)=>({
        ...defaultFullscreenProps,
        ...fullscreen
    });
const FullscreenContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const useFullscreen = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["makeUseContext"])("useFullscreen", "FullscreenContext", FullscreenContext);
function FullscreenContextProvider({ fullscreen: fullscreenProps, on, children }) {
    const { auto, ref } = resolveFullscreenProps(fullscreenProps);
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [disabled, setDisabled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    const [fullscreen, setFullscreen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const wasFullscreen = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const { getOwnerDocument } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useDocumentContext"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLayoutEffect"])({
        "FullscreenContextProvider.useLayoutEffect": ()=>{
            var _a, _b, _c, _d;
            const ownerDocument = getOwnerDocument();
            setDisabled(!((_d = (_c = (_b = (_a = ownerDocument.fullscreenEnabled) !== null && _a !== void 0 ? _a : ownerDocument.webkitFullscreenEnabled) !== null && _b !== void 0 ? _b : ownerDocument.mozFullScreenEnabled) !== null && _c !== void 0 ? _c : ownerDocument.msFullscreenEnabled) !== null && _d !== void 0 ? _d : false));
        }
    }["FullscreenContextProvider.useLayoutEffect"], [
        getOwnerDocument
    ]);
    const getFullscreenElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FullscreenContextProvider.useCallback[getFullscreenElement]": ()=>{
            var _a;
            const ownerDocument = getOwnerDocument();
            const fullscreenElement = ownerDocument.fullscreenElement || ownerDocument.webkitFullscreenElement || ownerDocument.mozFullScreenElement || ownerDocument.msFullscreenElement;
            return ((_a = fullscreenElement === null || fullscreenElement === void 0 ? void 0 : fullscreenElement.shadowRoot) === null || _a === void 0 ? void 0 : _a.fullscreenElement) || fullscreenElement;
        }
    }["FullscreenContextProvider.useCallback[getFullscreenElement]"], [
        getOwnerDocument
    ]);
    const enter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FullscreenContextProvider.useCallback[enter]": ()=>{
            const container = containerRef.current;
            try {
                if (container.requestFullscreen) {
                    container.requestFullscreen().catch({
                        "FullscreenContextProvider.useCallback[enter]": ()=>{}
                    }["FullscreenContextProvider.useCallback[enter]"]);
                } else if (container.webkitRequestFullscreen) {
                    container.webkitRequestFullscreen();
                } else if (container.mozRequestFullScreen) {
                    container.mozRequestFullScreen();
                } else if (container.msRequestFullscreen) {
                    container.msRequestFullscreen();
                }
            } catch (_) {}
        }
    }["FullscreenContextProvider.useCallback[enter]"], []);
    const exit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FullscreenContextProvider.useCallback[exit]": ()=>{
            if (!getFullscreenElement()) return;
            const ownerDocument = getOwnerDocument();
            try {
                if (ownerDocument.exitFullscreen) {
                    ownerDocument.exitFullscreen().catch({
                        "FullscreenContextProvider.useCallback[exit]": ()=>{}
                    }["FullscreenContextProvider.useCallback[exit]"]);
                } else if (ownerDocument.webkitExitFullscreen) {
                    ownerDocument.webkitExitFullscreen();
                } else if (ownerDocument.mozCancelFullScreen) {
                    ownerDocument.mozCancelFullScreen();
                } else if (ownerDocument.msExitFullscreen) {
                    ownerDocument.msExitFullscreen();
                }
            } catch (_) {}
        }
    }["FullscreenContextProvider.useCallback[exit]"], [
        getFullscreenElement,
        getOwnerDocument
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FullscreenContextProvider.useEffect": ()=>{
            const ownerDocument = getOwnerDocument();
            const listener = {
                "FullscreenContextProvider.useEffect.listener": ()=>{
                    setFullscreen(getFullscreenElement() === containerRef.current);
                }
            }["FullscreenContextProvider.useEffect.listener"];
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cleanup"])(...[
                "fullscreenchange",
                "webkitfullscreenchange",
                "mozfullscreenchange",
                "MSFullscreenChange"
            ].map({
                "FullscreenContextProvider.useEffect": (event)=>{
                    ownerDocument.addEventListener(event, listener);
                    return ({
                        "FullscreenContextProvider.useEffect": ()=>ownerDocument.removeEventListener(event, listener)
                    })["FullscreenContextProvider.useEffect"];
                }
            }["FullscreenContextProvider.useEffect"]));
        }
    }["FullscreenContextProvider.useEffect"], [
        getFullscreenElement,
        getOwnerDocument
    ]);
    const onEnterFullscreen = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "FullscreenContextProvider.useEventCallback[onEnterFullscreen]": ()=>{
            var _a;
            return (_a = on.enterFullscreen) === null || _a === void 0 ? void 0 : _a.call(on);
        }
    }["FullscreenContextProvider.useEventCallback[onEnterFullscreen]"]);
    const onExitFullscreen = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "FullscreenContextProvider.useEventCallback[onExitFullscreen]": ()=>{
            var _a;
            return (_a = on.exitFullscreen) === null || _a === void 0 ? void 0 : _a.call(on);
        }
    }["FullscreenContextProvider.useEventCallback[onExitFullscreen]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FullscreenContextProvider.useEffect": ()=>{
            if (fullscreen) {
                wasFullscreen.current = true;
            }
            if (wasFullscreen.current) {
                (fullscreen ? onEnterFullscreen : onExitFullscreen)();
            }
        }
    }["FullscreenContextProvider.useEffect"], [
        fullscreen,
        onEnterFullscreen,
        onExitFullscreen
    ]);
    const handleAutoFullscreen = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "FullscreenContextProvider.useEventCallback[handleAutoFullscreen]": ()=>{
            var _a;
            (_a = auto ? enter : null) === null || _a === void 0 ? void 0 : _a();
            return exit;
        }
    }["FullscreenContextProvider.useEventCallback[handleAutoFullscreen]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(handleAutoFullscreen, [
        handleAutoFullscreen
    ]);
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FullscreenContextProvider.useMemo[context]": ()=>({
                fullscreen,
                disabled,
                enter,
                exit
            })
    }["FullscreenContextProvider.useMemo[context]"], [
        fullscreen,
        disabled,
        enter,
        exit
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "FullscreenContextProvider.useImperativeHandle": ()=>context
    }["FullscreenContextProvider.useImperativeHandle"], [
        context
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        ref: containerRef,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["clsx"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cssClass"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLUGIN_FULLSCREEN"]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cssClass"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLASS_FULLSIZE"]))
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(FullscreenContext.Provider, {
        value: context
    }, children));
}
const EnterFullscreenIcon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createIcon"])("EnterFullscreen", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
    d: "M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"
}));
const ExitFullscreenIcon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createIcon"])("ExitFullscreen", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
    d: "M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"
}));
function FullscreenButton() {
    var _a;
    const { fullscreen, disabled, enter, exit } = useFullscreen();
    const { render } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxProps"])();
    if (disabled) return null;
    if (render.buttonFullscreen) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, (_a = render.buttonFullscreen) === null || _a === void 0 ? void 0 : _a.call(render, {
            fullscreen,
            disabled,
            enter,
            exit
        }));
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["IconButton"], {
        disabled: disabled,
        label: fullscreen ? "Exit Fullscreen" : "Enter Fullscreen",
        icon: fullscreen ? ExitFullscreenIcon : EnterFullscreenIcon,
        renderIcon: fullscreen ? render.iconExitFullscreen : render.iconEnterFullscreen,
        onClick: fullscreen ? exit : enter
    });
}
function Fullscreen({ augment, contains, addParent }) {
    augment(({ fullscreen, toolbar, ...restProps })=>({
            toolbar: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addToolbarButton"])(toolbar, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLUGIN_FULLSCREEN"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(FullscreenButton, null)),
            fullscreen: resolveFullscreenProps(fullscreen),
            ...restProps
        }));
    addParent(contains(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLUGIN_THUMBNAILS"]) ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLUGIN_THUMBNAILS"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_CONTROLLER"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createModule"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLUGIN_FULLSCREEN"], FullscreenContextProvider));
}
;
}}),
"[project]/node_modules/yet-another-react-lightbox/dist/plugins/slideshow/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Slideshow)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/yet-another-react-lightbox/dist/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/yet-another-react-lightbox/dist/types.js [app-client] (ecmascript)");
;
;
;
const defaultSlideshowProps = {
    autoplay: false,
    delay: 3000,
    ref: null
};
const resolveSlideshowProps = (slideshow)=>({
        ...defaultSlideshowProps,
        ...slideshow
    });
const SlideshowContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const useSlideshow = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["makeUseContext"])("useSlideshow", "SlideshowContext", SlideshowContext);
function SlideshowContextProvider({ slideshow, carousel: { finite }, on, children }) {
    const { autoplay, delay, ref } = resolveSlideshowProps(slideshow);
    const wasPlaying = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(autoplay);
    const [playing, setPlaying] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(autoplay);
    const scheduler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const slideStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const { slides, currentIndex } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxState"])();
    const { setTimeout, clearTimeout } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTimeouts"])();
    const { subscribe } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEvents"])();
    const { next } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useController"])();
    const disabled = slides.length === 0 || finite && currentIndex === slides.length - 1;
    const play = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SlideshowContextProvider.useCallback[play]": ()=>{
            if (!playing && !disabled) {
                setPlaying(true);
            }
        }
    }["SlideshowContextProvider.useCallback[play]"], [
        playing,
        disabled
    ]);
    const pause = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SlideshowContextProvider.useCallback[pause]": ()=>{
            if (playing) {
                setPlaying(false);
            }
        }
    }["SlideshowContextProvider.useCallback[pause]"], [
        playing
    ]);
    const cancelScheduler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SlideshowContextProvider.useCallback[cancelScheduler]": ()=>{
            clearTimeout(scheduler.current);
            scheduler.current = undefined;
        }
    }["SlideshowContextProvider.useCallback[cancelScheduler]"], [
        clearTimeout
    ]);
    const scheduleNextSlide = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "SlideshowContextProvider.useEventCallback[scheduleNextSlide]": ()=>{
            cancelScheduler();
            if (!playing || disabled || slideStatus.current === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_LOADING"] || slideStatus.current === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_PLAYING"]) {
                return;
            }
            scheduler.current = setTimeout({
                "SlideshowContextProvider.useEventCallback[scheduleNextSlide]": ()=>{
                    if (playing) {
                        slideStatus.current = undefined;
                        next();
                    }
                }
            }["SlideshowContextProvider.useEventCallback[scheduleNextSlide]"], delay);
        }
    }["SlideshowContextProvider.useEventCallback[scheduleNextSlide]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(scheduleNextSlide, [
        currentIndex,
        playing,
        scheduleNextSlide
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SlideshowContextProvider.useEffect": ()=>{
            if (playing && disabled) {
                setPlaying(false);
            }
        }
    }["SlideshowContextProvider.useEffect"], [
        currentIndex,
        playing,
        disabled
    ]);
    const onSlideshowStart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "SlideshowContextProvider.useEventCallback[onSlideshowStart]": ()=>{
            var _a;
            return (_a = on.slideshowStart) === null || _a === void 0 ? void 0 : _a.call(on);
        }
    }["SlideshowContextProvider.useEventCallback[onSlideshowStart]"]);
    const onSlideshowStop = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "SlideshowContextProvider.useEventCallback[onSlideshowStop]": ()=>{
            var _a;
            return (_a = on.slideshowStop) === null || _a === void 0 ? void 0 : _a.call(on);
        }
    }["SlideshowContextProvider.useEventCallback[onSlideshowStop]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SlideshowContextProvider.useEffect": ()=>{
            if (playing) {
                onSlideshowStart();
            } else if (wasPlaying.current) {
                onSlideshowStop();
            }
            wasPlaying.current = playing;
        }
    }["SlideshowContextProvider.useEffect"], [
        playing,
        onSlideshowStart,
        onSlideshowStop
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SlideshowContextProvider.useEffect": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cleanup"])(cancelScheduler, subscribe(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTIVE_SLIDE_LOADING"], {
                "SlideshowContextProvider.useEffect": ()=>{
                    slideStatus.current = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_LOADING"];
                    cancelScheduler();
                }
            }["SlideshowContextProvider.useEffect"]), subscribe(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTIVE_SLIDE_PLAYING"], {
                "SlideshowContextProvider.useEffect": ()=>{
                    slideStatus.current = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_PLAYING"];
                    cancelScheduler();
                }
            }["SlideshowContextProvider.useEffect"]), subscribe(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTIVE_SLIDE_ERROR"], {
                "SlideshowContextProvider.useEffect": ()=>{
                    slideStatus.current = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_ERROR"];
                    scheduleNextSlide();
                }
            }["SlideshowContextProvider.useEffect"]), subscribe(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ACTIVE_SLIDE_COMPLETE"], {
                "SlideshowContextProvider.useEffect": ()=>{
                    slideStatus.current = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SLIDE_STATUS_COMPLETE"];
                    scheduleNextSlide();
                }
            }["SlideshowContextProvider.useEffect"]))
    }["SlideshowContextProvider.useEffect"], [
        subscribe,
        cancelScheduler,
        scheduleNextSlide
    ]);
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "SlideshowContextProvider.useMemo[context]": ()=>({
                playing,
                disabled,
                play,
                pause
            })
    }["SlideshowContextProvider.useMemo[context]"], [
        playing,
        disabled,
        play,
        pause
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "SlideshowContextProvider.useImperativeHandle": ()=>context
    }["SlideshowContextProvider.useImperativeHandle"], [
        context
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(SlideshowContext.Provider, {
        value: context
    }, children);
}
const PlayIcon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createIcon"])("Play", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
    d: "M8 5v14l11-7z"
}));
const PauseIcon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createIcon"])("Pause", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
    d: "M6 19h4V5H6v14zm8-14v14h4V5h-4z"
}));
function SlideshowButton() {
    const { playing, disabled, play, pause } = useSlideshow();
    const { render } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxProps"])();
    const focusListeners = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLoseFocus"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useController"])().focus, disabled);
    if (render.buttonSlideshow) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, render.buttonSlideshow({
            playing,
            disabled,
            play,
            pause
        }));
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["IconButton"], {
        label: playing ? "Pause" : "Play",
        icon: playing ? PauseIcon : PlayIcon,
        renderIcon: playing ? render.iconSlideshowPause : render.iconSlideshowPlay,
        onClick: playing ? pause : play,
        disabled: disabled,
        ...focusListeners
    });
}
function Slideshow({ augment, addModule }) {
    augment(({ slideshow, toolbar, ...restProps })=>({
            toolbar: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addToolbarButton"])(toolbar, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLUGIN_SLIDESHOW"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(SlideshowButton, null)),
            slideshow: resolveSlideshowProps(slideshow),
            ...restProps
        }));
    addModule((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createModule"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLUGIN_SLIDESHOW"], SlideshowContextProvider));
}
;
}}),
"[project]/node_modules/yet-another-react-lightbox/dist/plugins/zoom/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Zoom)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/yet-another-react-lightbox/dist/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/yet-another-react-lightbox/dist/types.js [app-client] (ecmascript)");
;
;
;
const defaultZoomProps = {
    maxZoomPixelRatio: 1,
    zoomInMultiplier: 2,
    doubleTapDelay: 300,
    doubleClickDelay: 500,
    doubleClickMaxStops: 2,
    keyboardMoveDistance: 50,
    wheelZoomDistanceFactor: 100,
    pinchZoomDistanceFactor: 100,
    scrollToZoom: false
};
const resolveZoomProps = (zoom)=>({
        ...defaultZoomProps,
        ...zoom
    });
function useZoomAnimation(zoom, offsetX, offsetY, zoomWrapperRef) {
    const zoomAnimation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const zoomAnimationStart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const { zoom: zoomAnimationDuration } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxProps"])().animation;
    const reduceMotion = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useMotionPreference"])();
    const playZoomAnimation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "useZoomAnimation.useEventCallback[playZoomAnimation]": ()=>{
            var _a, _b, _c;
            (_a = zoomAnimation.current) === null || _a === void 0 ? void 0 : _a.cancel();
            zoomAnimation.current = undefined;
            if (zoomAnimationStart.current && (zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current)) {
                try {
                    zoomAnimation.current = (_c = (_b = zoomWrapperRef.current).animate) === null || _c === void 0 ? void 0 : _c.call(_b, [
                        {
                            transform: zoomAnimationStart.current
                        },
                        {
                            transform: `scale(${zoom}) translateX(${offsetX}px) translateY(${offsetY}px)`
                        }
                    ], {
                        duration: !reduceMotion ? zoomAnimationDuration !== null && zoomAnimationDuration !== void 0 ? zoomAnimationDuration : 500 : 0,
                        easing: zoomAnimation.current ? "ease-out" : "ease-in-out"
                    });
                } catch (err) {
                    console.error(err);
                }
                zoomAnimationStart.current = undefined;
                if (zoomAnimation.current) {
                    zoomAnimation.current.onfinish = ({
                        "useZoomAnimation.useEventCallback[playZoomAnimation]": ()=>{
                            zoomAnimation.current = undefined;
                        }
                    })["useZoomAnimation.useEventCallback[playZoomAnimation]"];
                }
            }
        }
    }["useZoomAnimation.useEventCallback[playZoomAnimation]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLayoutEffect"])(playZoomAnimation, [
        zoom,
        offsetX,
        offsetY,
        playZoomAnimation
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useZoomAnimation.useCallback": ()=>{
            zoomAnimationStart.current = (zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current) ? window.getComputedStyle(zoomWrapperRef.current).transform : undefined;
        }
    }["useZoomAnimation.useCallback"], [
        zoomWrapperRef
    ]);
}
function useZoomCallback(zoom, disabled) {
    const { on } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxProps"])();
    const onZoomCallback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "useZoomCallback.useEventCallback[onZoomCallback]": ()=>{
            var _a;
            if (!disabled) {
                (_a = on.zoom) === null || _a === void 0 ? void 0 : _a.call(on, {
                    zoom
                });
            }
        }
    }["useZoomCallback.useEventCallback[onZoomCallback]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(onZoomCallback, [
        zoom,
        onZoomCallback
    ]);
}
function useZoomProps() {
    const { zoom } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxProps"])();
    return resolveZoomProps(zoom);
}
function useZoomImageRect(slideRect, imageDimensions) {
    var _a, _b;
    let imageRect = {
        width: 0,
        height: 0
    };
    let maxImageRect = {
        width: 0,
        height: 0
    };
    const { currentSlide } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxState"])();
    const { imageFit } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxProps"])().carousel;
    const { maxZoomPixelRatio } = useZoomProps();
    if (slideRect && currentSlide) {
        const slide = {
            ...currentSlide,
            ...imageDimensions
        };
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isImageSlide"])(slide)) {
            const cover = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isImageFitCover"])(slide, imageFit);
            const width = Math.max(...(((_a = slide.srcSet) === null || _a === void 0 ? void 0 : _a.map((x)=>x.width)) || []).concat(slide.width ? [
                slide.width
            ] : []));
            const height = Math.max(...(((_b = slide.srcSet) === null || _b === void 0 ? void 0 : _b.map((x)=>x.height)) || []).concat(slide.height ? [
                slide.height
            ] : []));
            if (width > 0 && height > 0 && slideRect.width > 0 && slideRect.height > 0) {
                maxImageRect = cover ? {
                    width: Math.round(Math.min(width, slideRect.width / slideRect.height * height)),
                    height: Math.round(Math.min(height, slideRect.height / slideRect.width * width))
                } : {
                    width,
                    height
                };
                maxImageRect = {
                    width: maxImageRect.width * maxZoomPixelRatio,
                    height: maxImageRect.height * maxZoomPixelRatio
                };
                imageRect = cover ? {
                    width: Math.min(slideRect.width, maxImageRect.width, width),
                    height: Math.min(slideRect.height, maxImageRect.height, height)
                } : {
                    width: Math.round(Math.min(slideRect.width, slideRect.height / height * width, width)),
                    height: Math.round(Math.min(slideRect.height, slideRect.width / width * height, height))
                };
            }
        }
    }
    const maxZoom = imageRect.width ? Math.max((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["round"])(maxImageRect.width / imageRect.width, 5), 1) : 1;
    return {
        imageRect,
        maxZoom
    };
}
function distance(pointerA, pointerB) {
    return ((pointerA.clientX - pointerB.clientX) ** 2 + (pointerA.clientY - pointerB.clientY) ** 2) ** 0.5;
}
function scaleZoom(value, delta, factor = 100, clamp = 2) {
    return value * Math.min(1 + Math.abs(delta / factor), clamp) ** Math.sign(delta);
}
function useZoomSensors(zoom, maxZoom, disabled, changeZoom, changeOffsets, zoomWrapperRef) {
    const activePointers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    const lastPointerDown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const pinchZoomDistance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    const { globalIndex } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxState"])();
    const { getOwnerWindow } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useDocumentContext"])();
    const { containerRef, subscribeSensors } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useController"])();
    const { keyboardMoveDistance, zoomInMultiplier, wheelZoomDistanceFactor, scrollToZoom, doubleTapDelay, doubleClickDelay, doubleClickMaxStops, pinchZoomDistanceFactor } = useZoomProps();
    const translateCoordinates = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useZoomSensors.useCallback[translateCoordinates]": (event)=>{
            if (containerRef.current) {
                const { pageX, pageY } = event;
                const { scrollX, scrollY } = getOwnerWindow();
                const { left, top, width, height } = containerRef.current.getBoundingClientRect();
                return [
                    pageX - left - scrollX - width / 2,
                    pageY - top - scrollY - height / 2
                ];
            }
            return [];
        }
    }["useZoomSensors.useCallback[translateCoordinates]"], [
        containerRef,
        getOwnerWindow
    ]);
    const onKeyDown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "useZoomSensors.useEventCallback[onKeyDown]": (event)=>{
            const { key, metaKey, ctrlKey } = event;
            const meta = metaKey || ctrlKey;
            const preventDefault = {
                "useZoomSensors.useEventCallback[onKeyDown].preventDefault": ()=>{
                    event.preventDefault();
                    event.stopPropagation();
                }
            }["useZoomSensors.useEventCallback[onKeyDown].preventDefault"];
            if (zoom > 1) {
                const move = {
                    "useZoomSensors.useEventCallback[onKeyDown].move": (deltaX, deltaY)=>{
                        preventDefault();
                        changeOffsets(deltaX, deltaY);
                    }
                }["useZoomSensors.useEventCallback[onKeyDown].move"];
                if (key === "ArrowDown") {
                    move(0, keyboardMoveDistance);
                } else if (key === "ArrowUp") {
                    move(0, -keyboardMoveDistance);
                } else if (key === "ArrowLeft") {
                    move(-keyboardMoveDistance, 0);
                } else if (key === "ArrowRight") {
                    move(keyboardMoveDistance, 0);
                }
            }
            const handleChangeZoom = {
                "useZoomSensors.useEventCallback[onKeyDown].handleChangeZoom": (zoomValue)=>{
                    preventDefault();
                    changeZoom(zoomValue);
                }
            }["useZoomSensors.useEventCallback[onKeyDown].handleChangeZoom"];
            if (key === "+" || meta && key === "=") {
                handleChangeZoom(zoom * zoomInMultiplier);
            } else if (key === "-" || meta && key === "_") {
                handleChangeZoom(zoom / zoomInMultiplier);
            } else if (meta && key === "0") {
                handleChangeZoom(1);
            }
        }
    }["useZoomSensors.useEventCallback[onKeyDown]"]);
    const onWheel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "useZoomSensors.useEventCallback[onWheel]": (event)=>{
            if (event.ctrlKey || scrollToZoom) {
                if (Math.abs(event.deltaY) > Math.abs(event.deltaX)) {
                    event.stopPropagation();
                    changeZoom(scaleZoom(zoom, -event.deltaY, wheelZoomDistanceFactor), true, ...translateCoordinates(event));
                    return;
                }
            }
            if (zoom > 1) {
                event.stopPropagation();
                if (!scrollToZoom) {
                    changeOffsets(event.deltaX, event.deltaY);
                }
            }
        }
    }["useZoomSensors.useEventCallback[onWheel]"]);
    const clearPointer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useZoomSensors.useCallback[clearPointer]": (event)=>{
            const pointers = activePointers.current;
            pointers.splice(0, pointers.length, ...pointers.filter({
                "useZoomSensors.useCallback[clearPointer]": (p)=>p.pointerId !== event.pointerId
            }["useZoomSensors.useCallback[clearPointer]"]));
        }
    }["useZoomSensors.useCallback[clearPointer]"], []);
    const replacePointer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useZoomSensors.useCallback[replacePointer]": (event)=>{
            clearPointer(event);
            event.persist();
            activePointers.current.push(event);
        }
    }["useZoomSensors.useCallback[replacePointer]"], [
        clearPointer
    ]);
    const onPointerDown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "useZoomSensors.useEventCallback[onPointerDown]": (event)=>{
            var _a;
            const pointers = activePointers.current;
            if (event.pointerType === "mouse" && event.buttons > 1 || !((_a = zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current) === null || _a === void 0 ? void 0 : _a.contains(event.target))) {
                return;
            }
            if (zoom > 1) {
                event.stopPropagation();
            }
            const { timeStamp } = event;
            if (pointers.length === 0 && timeStamp - lastPointerDown.current < (event.pointerType === "touch" ? doubleTapDelay : doubleClickDelay)) {
                lastPointerDown.current = 0;
                changeZoom(zoom !== maxZoom ? zoom * Math.max(maxZoom ** (1 / doubleClickMaxStops), zoomInMultiplier) : 1, false, ...translateCoordinates(event));
            } else {
                lastPointerDown.current = timeStamp;
            }
            replacePointer(event);
            if (pointers.length === 2) {
                pinchZoomDistance.current = distance(pointers[0], pointers[1]);
            }
        }
    }["useZoomSensors.useEventCallback[onPointerDown]"]);
    const onPointerMove = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "useZoomSensors.useEventCallback[onPointerMove]": (event)=>{
            const pointers = activePointers.current;
            const activePointer = pointers.find({
                "useZoomSensors.useEventCallback[onPointerMove].activePointer": (p)=>p.pointerId === event.pointerId
            }["useZoomSensors.useEventCallback[onPointerMove].activePointer"]);
            if (pointers.length === 2 && pinchZoomDistance.current) {
                event.stopPropagation();
                replacePointer(event);
                const currentDistance = distance(pointers[0], pointers[1]);
                const delta = currentDistance - pinchZoomDistance.current;
                if (Math.abs(delta) > 0) {
                    changeZoom(scaleZoom(zoom, delta, pinchZoomDistanceFactor), true, ...pointers.map({
                        "useZoomSensors.useEventCallback[onPointerMove]": (x)=>translateCoordinates(x)
                    }["useZoomSensors.useEventCallback[onPointerMove]"]).reduce({
                        "useZoomSensors.useEventCallback[onPointerMove]": (acc, coordinate)=>coordinate.map({
                                "useZoomSensors.useEventCallback[onPointerMove]": (x, i)=>acc[i] + x / 2
                            }["useZoomSensors.useEventCallback[onPointerMove]"])
                    }["useZoomSensors.useEventCallback[onPointerMove]"]));
                    pinchZoomDistance.current = currentDistance;
                }
                return;
            }
            if (zoom > 1) {
                event.stopPropagation();
                if (activePointer) {
                    if (pointers.length === 1) {
                        changeOffsets((activePointer.clientX - event.clientX) / zoom, (activePointer.clientY - event.clientY) / zoom);
                    }
                    replacePointer(event);
                }
            }
        }
    }["useZoomSensors.useEventCallback[onPointerMove]"]);
    const onPointerUp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useZoomSensors.useCallback[onPointerUp]": (event)=>{
            const pointers = activePointers.current;
            if (pointers.length === 2 && pointers.find({
                "useZoomSensors.useCallback[onPointerUp]": (p)=>p.pointerId === event.pointerId
            }["useZoomSensors.useCallback[onPointerUp]"])) {
                pinchZoomDistance.current = undefined;
            }
            clearPointer(event);
        }
    }["useZoomSensors.useCallback[onPointerUp]"], [
        clearPointer
    ]);
    const cleanupSensors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useZoomSensors.useCallback[cleanupSensors]": ()=>{
            const pointers = activePointers.current;
            pointers.splice(0, pointers.length);
            lastPointerDown.current = 0;
            pinchZoomDistance.current = undefined;
        }
    }["useZoomSensors.useCallback[cleanupSensors]"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["usePointerEvents"])(subscribeSensors, onPointerDown, onPointerMove, onPointerUp, disabled);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(cleanupSensors, [
        globalIndex,
        cleanupSensors
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useZoomSensors.useEffect": ()=>{
            if (!disabled) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cleanup"])(cleanupSensors, subscribeSensors(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_KEY_DOWN"], onKeyDown), subscribeSensors(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_ON_WHEEL"], onWheel));
            }
            return ({
                "useZoomSensors.useEffect": ()=>{}
            })["useZoomSensors.useEffect"];
        }
    }["useZoomSensors.useEffect"], [
        disabled,
        subscribeSensors,
        cleanupSensors,
        onKeyDown,
        onWheel
    ]);
}
function useZoomState(imageRect, maxZoom, zoomWrapperRef) {
    const [zoom, setZoom] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    const [offsetX, setOffsetX] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [offsetY, setOffsetY] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const animate = useZoomAnimation(zoom, offsetX, offsetY, zoomWrapperRef);
    const { currentSlide, globalIndex } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxState"])();
    const { containerRect, slideRect } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useController"])();
    const { zoomInMultiplier } = useZoomProps();
    const currentSource = currentSlide && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isImageSlide"])(currentSlide) ? currentSlide.src : undefined;
    const disabled = !currentSource || !(zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLayoutEffect"])({
        "useZoomState.useLayoutEffect": ()=>{
            setZoom(1);
            setOffsetX(0);
            setOffsetY(0);
        }
    }["useZoomState.useLayoutEffect"], [
        globalIndex,
        currentSource
    ]);
    const changeOffsets = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useZoomState.useCallback[changeOffsets]": (dx, dy, targetZoom)=>{
            const newZoom = targetZoom || zoom;
            const newOffsetX = offsetX - (dx || 0);
            const newOffsetY = offsetY - (dy || 0);
            const maxOffsetX = (imageRect.width * newZoom - slideRect.width) / 2 / newZoom;
            const maxOffsetY = (imageRect.height * newZoom - slideRect.height) / 2 / newZoom;
            setOffsetX(Math.min(Math.abs(newOffsetX), Math.max(maxOffsetX, 0)) * Math.sign(newOffsetX));
            setOffsetY(Math.min(Math.abs(newOffsetY), Math.max(maxOffsetY, 0)) * Math.sign(newOffsetY));
        }
    }["useZoomState.useCallback[changeOffsets]"], [
        zoom,
        offsetX,
        offsetY,
        slideRect,
        imageRect.width,
        imageRect.height
    ]);
    const changeZoom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useZoomState.useCallback[changeZoom]": (targetZoom, rapid, dx, dy)=>{
            const newZoom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["round"])(Math.min(Math.max(targetZoom + 0.001 < maxZoom ? targetZoom : maxZoom, 1), maxZoom), 5);
            if (newZoom === zoom) return;
            if (!rapid) {
                animate();
            }
            changeOffsets(dx ? dx * (1 / zoom - 1 / newZoom) : 0, dy ? dy * (1 / zoom - 1 / newZoom) : 0, newZoom);
            setZoom(newZoom);
        }
    }["useZoomState.useCallback[changeZoom]"], [
        zoom,
        maxZoom,
        changeOffsets,
        animate
    ]);
    const handleControllerRectChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "useZoomState.useEventCallback[handleControllerRectChange]": ()=>{
            if (zoom > 1) {
                if (zoom > maxZoom) {
                    changeZoom(maxZoom, true);
                }
                changeOffsets();
            }
        }
    }["useZoomState.useEventCallback[handleControllerRectChange]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLayoutEffect"])(handleControllerRectChange, [
        containerRect.width,
        containerRect.height,
        handleControllerRectChange
    ]);
    const zoomIn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useZoomState.useCallback[zoomIn]": ()=>changeZoom(zoom * zoomInMultiplier)
    }["useZoomState.useCallback[zoomIn]"], [
        zoom,
        zoomInMultiplier,
        changeZoom
    ]);
    const zoomOut = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useZoomState.useCallback[zoomOut]": ()=>changeZoom(zoom / zoomInMultiplier)
    }["useZoomState.useCallback[zoomOut]"], [
        zoom,
        zoomInMultiplier,
        changeZoom
    ]);
    return {
        zoom,
        offsetX,
        offsetY,
        disabled,
        changeOffsets,
        changeZoom,
        zoomIn,
        zoomOut
    };
}
const ZoomControllerContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const useZoom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["makeUseContext"])("useZoom", "ZoomControllerContext", ZoomControllerContext);
function ZoomContextProvider({ children }) {
    const [zoomWrapper, setZoomWrapper] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    const { slideRect } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useController"])();
    const { imageRect, maxZoom } = useZoomImageRect(slideRect, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.imageDimensions);
    const { zoom, offsetX, offsetY, disabled, changeZoom, changeOffsets, zoomIn, zoomOut } = useZoomState(imageRect, maxZoom, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.zoomWrapperRef);
    useZoomCallback(zoom, disabled);
    useZoomSensors(zoom, maxZoom, disabled, changeZoom, changeOffsets, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.zoomWrapperRef);
    const zoomRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ZoomContextProvider.useMemo[zoomRef]": ()=>({
                zoom,
                maxZoom,
                offsetX,
                offsetY,
                disabled,
                zoomIn,
                zoomOut,
                changeZoom
            })
    }["ZoomContextProvider.useMemo[zoomRef]"], [
        zoom,
        maxZoom,
        offsetX,
        offsetY,
        disabled,
        zoomIn,
        zoomOut,
        changeZoom
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(useZoomProps().ref, {
        "ZoomContextProvider.useImperativeHandle": ()=>zoomRef
    }["ZoomContextProvider.useImperativeHandle"], [
        zoomRef
    ]);
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ZoomContextProvider.useMemo[context]": ()=>({
                ...zoomRef,
                setZoomWrapper
            })
    }["ZoomContextProvider.useMemo[context]"], [
        zoomRef,
        setZoomWrapper
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(ZoomControllerContext.Provider, {
        value: context
    }, children);
}
const ZoomInIcon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createIcon"])("ZoomIn", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
    d: "M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"
}), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
    d: "M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"
})));
const ZoomOutIcon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createIcon"])("ZoomOut", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
    d: "M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z"
}));
const ZoomButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(function ZoomButton({ zoomIn, onLoseFocus }, ref) {
    const wasEnabled = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const wasFocused = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const { zoom, maxZoom, zoomIn: zoomInCallback, zoomOut: zoomOutCallback, disabled: zoomDisabled } = useZoom();
    const { render } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxProps"])();
    const disabled = zoomDisabled || (zoomIn ? zoom >= maxZoom : zoom <= 1);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ZoomButton.ZoomButton.useEffect": ()=>{
            if (disabled && wasEnabled.current && wasFocused.current) {
                onLoseFocus();
            }
            if (!disabled) {
                wasEnabled.current = true;
            }
        }
    }["ZoomButton.ZoomButton.useEffect"], [
        disabled,
        onLoseFocus
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["IconButton"], {
        ref: ref,
        disabled: disabled,
        label: zoomIn ? "Zoom in" : "Zoom out",
        icon: zoomIn ? ZoomInIcon : ZoomOutIcon,
        renderIcon: zoomIn ? render.iconZoomIn : render.iconZoomOut,
        onClick: zoomIn ? zoomInCallback : zoomOutCallback,
        onFocus: ()=>{
            wasFocused.current = true;
        },
        onBlur: ()=>{
            wasFocused.current = false;
        }
    });
});
function ZoomButtonsGroup() {
    const zoomInRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const zoomOutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const { focus } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useController"])();
    const focusSibling = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ZoomButtonsGroup.useCallback[focusSibling]": (sibling)=>{
            var _a, _b;
            if (!((_a = sibling.current) === null || _a === void 0 ? void 0 : _a.disabled)) {
                (_b = sibling.current) === null || _b === void 0 ? void 0 : _b.focus();
            } else {
                focus();
            }
        }
    }["ZoomButtonsGroup.useCallback[focusSibling]"], [
        focus
    ]);
    const focusZoomIn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ZoomButtonsGroup.useCallback[focusZoomIn]": ()=>focusSibling(zoomInRef)
    }["ZoomButtonsGroup.useCallback[focusZoomIn]"], [
        focusSibling
    ]);
    const focusZoomOut = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ZoomButtonsGroup.useCallback[focusZoomOut]": ()=>focusSibling(zoomOutRef)
    }["ZoomButtonsGroup.useCallback[focusZoomOut]"], [
        focusSibling
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(ZoomButton, {
        zoomIn: true,
        ref: zoomInRef,
        onLoseFocus: focusZoomOut
    }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(ZoomButton, {
        ref: zoomOutRef,
        onLoseFocus: focusZoomIn
    }));
}
function ZoomToolbarControl() {
    const { render } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxProps"])();
    const zoomRef = useZoom();
    if (render.buttonZoom) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, render.buttonZoom(zoomRef));
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(ZoomButtonsGroup, null);
}
function isResponsiveImageSlide(slide) {
    var _a;
    return (((_a = slide.srcSet) === null || _a === void 0 ? void 0 : _a.length) || 0) > 0;
}
function reducer({ current, preload }, { type, source }) {
    switch(type){
        case "fetch":
            if (!current) {
                return {
                    current: source
                };
            }
            return {
                current,
                preload: source
            };
        case "done":
            if (source === preload) {
                return {
                    current: source
                };
            }
            return {
                current,
                preload
            };
        default:
            throw new Error(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UNKNOWN_ACTION_TYPE"]);
    }
}
function ResponsiveImage(props) {
    var _a, _b;
    const [{ current, preload }, dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReducer"])(reducer, {});
    const { slide: image, rect, imageFit, render, interactive } = props;
    const srcSet = image.srcSet.sort((a, b)=>a.width - b.width);
    const width = (_a = image.width) !== null && _a !== void 0 ? _a : srcSet[srcSet.length - 1].width;
    const height = (_b = image.height) !== null && _b !== void 0 ? _b : srcSet[srcSet.length - 1].height;
    const cover = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isImageFitCover"])(image, imageFit);
    const maxWidth = Math.max(...srcSet.map((x)=>x.width));
    const targetWidth = Math.min((cover ? Math.max : Math.min)(rect.width, width * (rect.height / height)), maxWidth);
    const pixelDensity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["devicePixelRatio"])();
    const handleResize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "ResponsiveImage.useEventCallback[handleResize]": ()=>{
            var _a;
            const targetSource = (_a = srcSet.find({
                "ResponsiveImage.useEventCallback[handleResize]": (x)=>x.width >= targetWidth * pixelDensity
            }["ResponsiveImage.useEventCallback[handleResize]"])) !== null && _a !== void 0 ? _a : srcSet[srcSet.length - 1];
            if (!current || srcSet.findIndex({
                "ResponsiveImage.useEventCallback[handleResize]": (x)=>x.src === current
            }["ResponsiveImage.useEventCallback[handleResize]"]) < srcSet.findIndex({
                "ResponsiveImage.useEventCallback[handleResize]": (x)=>x === targetSource
            }["ResponsiveImage.useEventCallback[handleResize]"])) {
                dispatch({
                    type: "fetch",
                    source: targetSource.src
                });
            }
        }
    }["ResponsiveImage.useEventCallback[handleResize]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLayoutEffect"])(handleResize, [
        rect.width,
        rect.height,
        pixelDensity,
        handleResize
    ]);
    const handlePreload = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useEventCallback"])({
        "ResponsiveImage.useEventCallback[handlePreload]": (currentPreload)=>dispatch({
                type: "done",
                source: currentPreload
            })
    }["ResponsiveImage.useEventCallback[handlePreload]"]);
    const style = {
        WebkitTransform: !interactive ? "translateZ(0)" : "initial"
    };
    if (!cover) {
        Object.assign(style, rect.width / rect.height < width / height ? {
            width: "100%",
            height: "auto"
        } : {
            width: "auto",
            height: "100%"
        });
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, preload && preload !== current && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ImageSlide"], {
        key: "preload",
        ...props,
        offset: undefined,
        slide: {
            ...image,
            src: preload,
            srcSet: undefined
        },
        style: {
            position: "absolute",
            visibility: "hidden",
            ...style
        },
        onLoad: ()=>handlePreload(preload),
        render: {
            ...render,
            iconLoading: ()=>null,
            iconError: ()=>null
        }
    }), current && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ImageSlide"], {
        key: "current",
        ...props,
        slide: {
            ...image,
            src: current,
            srcSet: undefined
        },
        style: style
    }));
}
function ZoomWrapper({ render, slide, offset, rect }) {
    var _a;
    const [imageDimensions, setImageDimensions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    const zoomWrapperRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const { zoom, maxZoom, offsetX, offsetY, setZoomWrapper } = useZoom();
    const interactive = zoom > 1;
    const { carousel, on } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxProps"])();
    const { currentIndex } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLightboxState"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useLayoutEffect"])({
        "ZoomWrapper.useLayoutEffect": ()=>{
            if (offset === 0) {
                setZoomWrapper({
                    zoomWrapperRef,
                    imageDimensions
                });
                return ({
                    "ZoomWrapper.useLayoutEffect": ()=>setZoomWrapper(undefined)
                })["ZoomWrapper.useLayoutEffect"];
            }
            return ({
                "ZoomWrapper.useLayoutEffect": ()=>{}
            })["ZoomWrapper.useLayoutEffect"];
        }
    }["ZoomWrapper.useLayoutEffect"], [
        offset,
        imageDimensions,
        setZoomWrapper
    ]);
    let rendered = (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, {
        slide,
        offset,
        rect,
        zoom,
        maxZoom
    });
    if (!rendered && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isImageSlide"])(slide)) {
        const slideProps = {
            slide,
            offset,
            rect,
            render,
            imageFit: carousel.imageFit,
            imageProps: carousel.imageProps,
            onClick: offset === 0 ? ()=>{
                var _a;
                return (_a = on.click) === null || _a === void 0 ? void 0 : _a.call(on, {
                    index: currentIndex
                });
            } : undefined
        };
        rendered = isResponsiveImageSlide(slide) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(ResponsiveImage, {
            ...slideProps,
            slide: slide,
            interactive: interactive,
            rect: offset === 0 ? {
                width: rect.width * zoom,
                height: rect.height * zoom
            } : rect
        }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ImageSlide"], {
            onLoad: (img)=>setImageDimensions({
                    width: img.naturalWidth,
                    height: img.naturalHeight
                }),
            ...slideProps
        });
    }
    if (!rendered) return null;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        ref: zoomWrapperRef,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["clsx"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cssClass"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLASS_FULLSIZE"]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cssClass"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLASS_FLEX_CENTER"]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cssClass"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLASS_SLIDE_WRAPPER"]), interactive && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cssClass"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CLASS_SLIDE_WRAPPER_INTERACTIVE"])),
        style: offset === 0 ? {
            transform: `scale(${zoom}) translateX(${offsetX}px) translateY(${offsetY}px)`
        } : undefined
    }, rendered);
}
const Zoom = ({ augment, addModule })=>{
    augment(({ zoom: zoomProps, toolbar, render, controller, ...restProps })=>{
        const zoom = resolveZoomProps(zoomProps);
        return {
            zoom,
            toolbar: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addToolbarButton"])(toolbar, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLUGIN_ZOOM"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(ZoomToolbarControl, null)),
            render: {
                ...render,
                slide: (props)=>{
                    var _a;
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isImageSlide"])(props.slide) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(ZoomWrapper, {
                        render: render,
                        ...props
                    }) : (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, props);
                }
            },
            controller: {
                ...controller,
                preventDefaultWheelY: zoom.scrollToZoom
            },
            ...restProps
        };
    });
    addModule((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createModule"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$yet$2d$another$2d$react$2d$lightbox$2f$dist$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLUGIN_ZOOM"], ZoomContextProvider));
};
;
}}),
}]);

//# sourceMappingURL=node_modules_1cae9a99._.js.map