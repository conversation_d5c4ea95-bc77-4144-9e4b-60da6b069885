(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/property/DetailMap.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$goongmaps$2f$goong$2d$js$2f$dist$2f$goong$2d$js$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@goongmaps/goong-js/dist/goong-js.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
const isBrowser = "object" !== "undefined";
const DetailMap = ({ property, center })=>{
    _s();
    const mapContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const mapRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DetailMap.useEffect": ()=>{
            if (!isBrowser || !mapContainerRef.current || !center) {
                if (mapRef.current) {
                    mapRef.current.remove();
                    mapRef.current = null;
                }
                return;
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$goongmaps$2f$goong$2d$js$2f$dist$2f$goong$2d$js$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].accessToken = `${"TURBOPACK compile-time value", "81Fs1nNxdnePjpwvhjbtlVJCm12lOfTpmOmYtWR4"}`;
            const map = mapRef.current;
            if (!map) {
                // Khởi tạo bản đồ lần đầu tiên với vị trí center nhận được qua prop
                const goongMap = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$goongmaps$2f$goong$2d$js$2f$dist$2f$goong$2d$js$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Map({
                    container: mapContainerRef.current,
                    style: "https://tiles.goong.io/assets/goong_map_web.json",
                    center: [
                        center.longitude,
                        center.latitude
                    ],
                    zoom: 15
                });
                mapRef.current = goongMap;
            } else {
                // Nếu bản đồ đã được khởi tạo, chỉ cập nhật vị trí trung tâm nếu prop center thay đổi
                const currentCenter = mapRef.current.getCenter();
                const currentZoom = mapRef.current.getZoom();
                const centerHasChanged = currentCenter.lat !== center.latitude || currentCenter.lng !== center.longitude;
                const zoomHasChanged = currentZoom !== zoom;
                if (centerHasChanged || zoomHasChanged) {
                    map.flyTo({
                        center: [
                            center.longitude,
                            center.latitude
                        ],
                        zoom: zoom,
                        essential: true
                    });
                } else {
                    console.log("GoongMap Effect: Prop center/zoom không đổi so với trạng thái hiện tại, BỎ QUA cập nhật bản đồ.");
                }
            }
            return ({
                "DetailMap.useEffect": ()=>{
                    if (mapRef.current) {
                        mapRef.current.remove();
                        mapRef.current = null;
                    }
                }
            })["DetailMap.useEffect"];
        }
    }["DetailMap.useEffect"], [
        center
    ]);
    // Effect dọn dẹp bản đồ cuối cùng khi unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DetailMap.useEffect": ()=>{
            return ({
                "DetailMap.useEffect": ()=>{
                    if (mapRef.current) {
                        mapRef.current.remove();
                        mapRef.current = null;
                    }
                }
            })["DetailMap.useEffect"];
        }
    }["DetailMap.useEffect"], []);
    // --- Effect xử lý Marker ---
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DetailMap.useEffect": ()=>{
            if (!mapRef.current || !property) {
                return;
            }
            const map = mapRef.current;
            // Xóa marker cũ
            if (map._currentMarkers) {
                map._currentMarkers.forEach({
                    "DetailMap.useEffect": (marker)=>marker.remove()
                }["DetailMap.useEffect"]);
            }
            map._currentMarkers = [];
            if (property) {
                if (property.latitude !== undefined && property.longitude !== undefined) {
                    try {
                        // --- TẠO PHẦN TỬ DOM TÙY CHỈNH CHO MARKER ---
                        const el = document.createElement("div");
                        el.className = "marker";
                        el.style.backgroundImage = "url(/detail_marker.png)";
                        el.style.width = "50px";
                        el.style.height = "50px";
                        el.style.backgroundSize = "cover";
                        el.style.cursor = "pointer";
                        const marker = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$goongmaps$2f$goong$2d$js$2f$dist$2f$goong$2d$js$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Marker(el, {
                            anchor: "bottom"
                        }).setLngLat([
                            property.longitude,
                            property.latitude
                        ]).addTo(map);
                        map._currentMarkers.push(marker);
                    } catch (markerError) {
                        console.error(`GoongMap Effect Markers: Lỗi khi tạo hoặc thêm marker:`, property, markerError);
                    }
                }
            } else {
                console.log("GoongMap Effect Marker: Không có marker để thêm.");
            }
            // Cleanup cho markers (xóa markers khi prop markers thay đổi hoặc unmount)
            return ({
                "DetailMap.useEffect": ()=>{
                    if (mapRef.current && mapRef.current._currentMarkers) {
                        mapRef.current._currentMarkers.forEach({
                            "DetailMap.useEffect": (marker)=>marker.remove()
                        }["DetailMap.useEffect"]);
                    }
                }
            })["DetailMap.useEffect"];
        }
    }["DetailMap.useEffect"], [
        property
    ]); // Dependencies: Chỉ chạy lại khi prop markers thay đổi
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `w-full h-[350px] relative z-0 opacity-75 cursor-not-allowed`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            ref: mapContainerRef,
            className: "w-full h-full"
        }, void 0, false, {
            fileName: "[project]/components/property/DetailMap.jsx",
            lineNumber: 129,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/property/DetailMap.jsx",
        lineNumber: 128,
        columnNumber: 5
    }, this);
};
_s(DetailMap, "QAEdrTKB3jFeWON1SV8mAvhy3fw=");
_c = DetailMap;
const __TURBOPACK__default__export__ = DetailMap;
var _c;
__turbopack_context__.k.register(_c, "DetailMap");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/property/DetailMap.jsx [app-client] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/components/property/DetailMap.jsx [app-client] (ecmascript)"));
}}),
}]);

//# sourceMappingURL=components_property_DetailMap_jsx_2fa023e2._.js.map