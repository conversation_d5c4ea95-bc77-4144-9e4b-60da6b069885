﻿ 
-- Database: yezhome

-- DROP DATABASE IF EXISTS "yezhome";

CREATE DATABASE "yezhome"
    WITH
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'Vietnamese_Vietnam.1258'
    LC_CTYPE = 'Vietnamese_Vietnam.1258'
    LOCALE_PROVIDER = 'libc'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1
    IS_TEMPLATE = False;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION pgcrypto;

-- Users Table
CREATE TABLE "AppUser" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "FullName" TEXT NOT NULL,
    "Email" VARCHAR(255) UNIQUE NOT NULL,
    "PasswordHash" VARCHAR(255) NOT NULL,
    "PasswordSalt" VARCHAR(255) NOT NULL,
    "UserType" VARCHAR(50) CHECK ("UserType" IN ('Seller', 'Buyer', 'Admin')),
    "Phone" VARCHAR(11) NOT NULL,
    "Phone2" VARCHAR(11) NULL,
    "Phone3" VARCHAR(11) NULL,
    "LastLogin" TIMESTAMP NULL,
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMESTAMP NULL,
    "TotalSpent" NUMERIC(20,2) DEFAULT 0, -- Tổng số tiền đã sử dụng
    "MemberRank" VARCHAR(50) NOT NULL DEFAULT 'default' CHECK ("MemberRank" IN ('default', 'bronze', 'sliver', 'gold', 'platinum', 'diamond')),
    "AvatarImage" VARCHAR(255) NULL,
    "TransferCode" VARCHAR(20) UNIQUE
);

-- Admin Roles Table
CREATE TABLE "AdminRole" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "Code" VARCHAR(20) UNIQUE NOT NULL,
    "RoleName" VARCHAR(100) UNIQUE NOT NULL
);

-- User-Role Mapping Table
CREATE TABLE "UserRole" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "UserID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "RoleID" UUID NOT NULL REFERENCES "AdminRole"("Id") ON DELETE CASCADE
);

-- Location Tables
CREATE TABLE "City" (
    "Id" INTEGER PRIMARY KEY,
    "Name" VARCHAR(255) NOT NULL,
    "Slug" VARCHAR(255) NOT NULL,
    "Type" VARCHAR(20) NOT NULL,
    "NameWithType" VARCHAR(255) NOT NULL
);

CREATE TABLE "District" (
    "Id" INTEGER PRIMARY KEY,
    "Name" VARCHAR(255) NOT NULL, --"Long Xuyên",
    "Slug" VARCHAR(255) NOT NULL, -- "long-xuyen",
    "Type" VARCHAR(20) NOT NULL, -- "thanh-pho",
    "NameWithType" VARCHAR(255) NOT NULL, -- "Thành phố Long Xuyên",
    "Path" VARCHAR(255) NOT NULL, -- "Long Xuyên, An Giang",
    "PathWithType" VARCHAR(255) NOT NULL, -- "Thành phố Long Xuyên, Tỉnh An Giang",
    "CityId" INTEGER REFERENCES "City"("Id")
);

CREATE TABLE "Ward" (
    "Id" INTEGER PRIMARY KEY,
    "Name" VARCHAR(255) NOT NULL,
    "Slug" VARCHAR(255) NOT NULL, -- "my-binh",
    "Type" VARCHAR(20) NOT NULL, -- "phuong",
    "NameWithType" VARCHAR(255) NOT NULL, -- "Phường Mỹ Bình",
    "Path" VARCHAR(255) NOT NULL, -- "Mỹ Bình, Long Xuyên, An Giang",
    "PathWithType" VARCHAR(255) NOT NULL, -- "Phường Mỹ Bình, Thành phố Long Xuyên, Tỉnh An Giang",
    "DistrictId" INTEGER REFERENCES "District"("Id")
);

CREATE TABLE "Street" (
    "Id" INTEGER PRIMARY KEY,
    "Name" VARCHAR(255) NOT NULL,
    "DistrictId" INTEGER REFERENCES "District"("Id")
);

-- Project Table
CREATE TABLE "Projects" (
    "Id" INTEGER PRIMARY KEY,
    "ProjectName" VARCHAR(255),
    "WardId" INTEGER REFERENCES "Ward"("Id"),
    "StreetId" INTEGER REFERENCES "Street"("Id")
);

--  Table
CREATE TABLE "Property" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "OwnerID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "Name" VARCHAR(255) NOT NULL,
    "Slug" VARCHAR(255) NOT NULL,
    "PostType" VARCHAR(5) NOT NULL, -- CHECK ("Type" IN ('Sale', 'Rent')),
    "PropertyType" VARCHAR(20) NOT NULL, -- CHECK ("Type" IN ('House', 'Apartment')),
    "CityId" INTEGER REFERENCES "City"("Id"),
    "DistrictId" INTEGER REFERENCES "District"("Id"),
    "StreetId" INTEGER REFERENCES "Street"("Id"),
    "WardId" INTEGER REFERENCES "Ward"("Id"),
    "Address" VARCHAR(500) NOT NULL, -- địa chỉ 
    "Area" NUMERIC(10,2), -- diện tích
    "Price" NUMERIC(18,2) NOT NULL, -- giá
    "Latitude" DECIMAL(9,6) NOT NULL,
    "Longitude" DECIMAL(9,6) NOT NULL,
    "VideoUrl" VARCHAR(500),
    "Floors" INTEGER, -- tầng 
    "Rooms" INTEGER, -- số phòng ngủ
    "Toilets" INTEGER, -- số toalet
    "Direction" VARCHAR(20), -- hướng nhà 
    "BalconyDirection" VARCHAR(20), -- hướng ban công
    "Legality" VARCHAR(30), -- tình trạng pháp lý (sổ đỏ/sổ hồng, ...)
    "Interior" VARCHAR(30), -- nội thất (đầy đủ, cơ bản, nhà trống)
    "Width" INTEGER, -- mặt tiền rộng ...m
    "RoadWidth" INTEGER, -- đường vào rộng ...m
    "Description" TEXT, -- mô tả 
    "Overview" TEXT, -- tóm tắt 
    "PlaceData" TEXT, -- 
    "Policies" TEXT, -- quy định (vd không nuôi chó, không nuôi heo, ...)
    "Neighborhood" TEXT, -- tiện ích xung quanh (vd gần bệnh viện, trường học, có thể add vào chung phần mô tả, phần này add thêm, add đâu cũng được)
    "Status" VARCHAR(20) DEFAULT 'draft', -- CHECK ("Status" IN ('Pending', 'Approved', 'Rejected', 'Draft')),    
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMESTAMP NULL,
    "PostPrice" NUMERIC(20,2) NOT NULL DEFAULT 55000, -- Phí đăng bài
    "IsHighlighted" BOOLEAN DEFAULT FALSE,
    "IsAutoRenew" BOOLEAN DEFAULT FALSE,
    "ExpiresAt" TIMESTAMP DEFAULT (now() + interval '10 days'),
    "UpdateRemainingTimes" INTEGER -- số lần được chỉnh sửa sau khi review (tối đa 5 lần)
);

CREATE TABLE IF NOT EXISTS "PropertyInvoice"
(
    "Id" uuid NOT NULL,
    "InvoiceCode" character varying(50) COLLATE pg_catalog."default" NOT NULL,
    "PropertyId"  UUID NOT NULL REFERENCES "Property"("Id"),
    "IsHighlighted" boolean DEFAULT false,
    "Price" numeric NOT NULL DEFAULT 55000,
    "HighlightFee" numeric DEFAULT 0,
    "Total" numeric,
    "CreatedBy" uuid,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "PropertyInvoice_pkey" PRIMARY KEY ("Id")
);


-- Contact Requests Table
CREATE TABLE "ContactRequest" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "Name" VARCHAR(255) NOT NULL,
    "Email" VARCHAR(255) NOT NULL,
    "Phone" VARCHAR(20) NOT NULL,
    "SentAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "PropertyId" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "AgentId" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "UserId" UUID NULL REFERENCES "AppUser"("Id") ON DELETE SET NULL,
    "Note" TEXT NULL, -- Ghi chú thêm từ khách hàng hoặc agent
    "Status" VARCHAR(20) DEFAULT 'Pending',
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMESTAMP NULL
);

-- Indexes for faster queries
CREATE INDEX "IDX_ContactRequest_Property" ON "ContactRequest"("PropertyId");
CREATE INDEX "IDX_ContactRequest_Agent" ON "ContactRequest"("AgentId");
CREATE INDEX "IDX_ContactRequest_User" ON "ContactRequest"("UserId");
CREATE INDEX "IDX_ContactRequest_SentAt" ON "ContactRequest"("SentAt");
CREATE INDEX "IDX_ContactRequest_Status" ON "ContactRequest"("Status");

-- Property Reviews Table
CREATE TABLE "PropertyReview" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "PropertyID" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "BuyerID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "Rating" INT CHECK ("Rating" BETWEEN 1 AND 5),
    "ReviewText" TEXT,
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMESTAMP NULL
);

-- Owner Reviews Table
CREATE TABLE "OwnerReview" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "OwnerID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "BuyerID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "Rating" INT CHECK ("Rating" BETWEEN 1 AND 5),
    "ReviewText" TEXT,
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMESTAMP NULL
);

-- Property Status Logs Table
CREATE TABLE "PropertyStatusLog" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "PropertyID" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "Status" VARCHAR(50),
    "ChangedBy" UUID NOT NULL REFERENCES "AppUser"("Id"),
    "ChangedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "Comment" TEXT
);

-- Blog Posts Table
CREATE TABLE "BlogPost" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "AuthorID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "Title" VARCHAR(255) NOT NULL,
    "Slug" VARCHAR(500) NOT NULL,
    "Content" TEXT NOT NULL,
    "FeaturedImage" VARCHAR(500),
    "Tags" VARCHAR(255),
    "IsFeature" BOOLEAN DEFAULT FALSE,
    "Status" VARCHAR(50) DEFAULT 'Draft' CHECK ("Status" IN ('Draft', 'Published')),
    "PublishedAt" TIMESTAMP NULL,
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMESTAMP NULL
);

-- Blog Comments Table
CREATE TABLE "BlogComment" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "PostID" UUID NOT NULL REFERENCES "BlogPost"("Id") ON DELETE CASCADE,
    "UserID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "CommentText" TEXT NOT NULL,
    "CreatedBy" UUID NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    
    "UpdatedBy" UUID NULL,
    "UpdatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN DEFAULT FALSE,
    "DeletedAt" TIMESTAMP NULL
);

-- Media Table for Property (Images/Videos)
CREATE TABLE "PropertyMedia" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "PropertyID" UUID NULL,
    "MediaType" VARCHAR(20) NOT NULL,
    "MediaURL" VARCHAR(500) NOT NULL,
    "UploadedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "Caption" VARCHAR(50),
    "IsAvatar" BOOLEAN DEFAULT FALSE,
    "FilePath" TEXT NULL,
    "ThumbnailPath" TEXT NULL,
    "SmallPath" TEXT NULL,
    "MediumPath" TEXT NULL,
    "LargePath" TEXT NULL
);

-- Favorites Table for Users to Follow 
CREATE TABLE "UserFavorite" (
    "Id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "UserID" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "PropertyID" UUID NOT NULL REFERENCES "Property"("Id") ON DELETE CASCADE,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_favorite UNIQUE ("UserID", "PropertyID")
);

CREATE TABLE "Wallets" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "UserId" UUID UNIQUE NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "Balance" NUMERIC(20,2) DEFAULT 0 -- Số dư ví
);

CREATE TABLE "WalletTransactions" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "UserId" UUID NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "Amount" NUMERIC(20,2) NOT NULL,
    "Type" VARCHAR(50) NOT NULL CHECK ("Type" IN ('TOP_UP', 'PAYMENT_POST', 'PAYMENT_HIGHLIGHT')), --type = 'TOP_UP' → Nạp tiền 🔹 type = 'PAYMENT_POST' → Tiêu tiền 🔹 type = 'PAYMENT_HIGHLIGHT' → Tiêu tiền
    "Description" TEXT NOT NULL,
    "CreatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE "MemberRankings" (
    "Id" UUID,
    "RankName" VARCHAR(50) PRIMARY KEY,
    "MinSpent" NUMERIC(20,2),
    "MaxSpent" NUMERIC(20,2)
);

INSERT INTO "MemberRankings" ("RankName", "MinSpent", "MaxSpent") VALUES
('diamond', 300000000, NULL),
('platinum', 100000000, 299999999),
('gold', 50000000, 99999999),
('silver', 20000000, 49999999),
('bronze', 10000000, 19999999),
('default', 0, 9999999);

CREATE TABLE "HighlightFees" (
    "Id" UUID,
    "RankName" VARCHAR(50) PRIMARY KEY REFERENCES "MemberRankings"("RankName"),
    "Fee" NUMERIC(20,2) NOT NULL
);

INSERT INTO "HighlightFees" ("RankName", "Fee") VALUES
('diamond', 30000),
('platinum', 35000),
('gold', 40000),
('silver', 45000),
('bronze', 50000),
('default', 55000);

CREATE TABLE "Notification" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "UserId" UUID NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,  -- NULL nếu là thông báo chung
    "Type" VARCHAR(50) NOT NULL CHECK ("Type" IN ('promotion', 'system', 'transaction', 'contact')),
    "Title" VARCHAR(255) NOT NULL,
    "Message" TEXT NOT NULL,
    "IsRead" BOOLEAN DEFAULT FALSE,
    "CreatedAt" TIMESTAMP DEFAULT now()
);

CREATE INDEX idx_notifications_user ON "Notification" ("UserId", "IsRead", "CreatedAt" DESC);
CREATE INDEX idx_notifications_type ON "Notification" ("Type");

CREATE TABLE "NotificationPreferences" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "UserId" UUID UNIQUE NOT NULL REFERENCES "AppUser"("Id") ON DELETE CASCADE,
    "ReceivePromotions" BOOLEAN DEFAULT TRUE,
    "ReceiveWalletUpdates" BOOLEAN DEFAULT TRUE,
    "ReceiveNews" BOOLEAN DEFAULT TRUE,
    "ReceiveCustomerMessages" BOOLEAN DEFAULT TRUE,
    "CreatedAt" TIMESTAMP DEFAULT now()
);

-- Insert default role for admin role
INSERT INTO "AdminRole" ("Code", "RoleName") VALUES
    ('SYSADMIN', 'System Admin'),
    ('SEOEDITOR', 'SEO Editor'),
    ('PROPADMIN', 'Property Admin');


-- Insert default location Vietnam data
INSERT INTO "City" ("Id", "Name", "Slug", "Type", "NameWithType") VALUES
(89, 'An Giang', 'an-giang', 'tinh', 'Tỉnh An Giang'),
(62, 'Kon Tum', 'kon-tum', 'tinh', 'Tỉnh Kon Tum'),
(67, 'Đắk Nông', 'dak-nong', 'tinh', 'Tỉnh Đắk Nông'),
(94, 'Sóc Trăng', 'soc-trang', 'tinh', 'Tỉnh Sóc Trăng'),
(70, 'Bình Phước', 'binh-phuoc', 'tinh', 'Tỉnh Bình Phước'),
(33, 'Hưng Yên', 'hung-yen', 'tinh', 'Tỉnh Hưng Yên'),
(38, 'Thanh Hóa', 'thanh-hoa', 'tinh', 'Tỉnh Thanh Hóa'),
(45, 'Quảng Trị', 'quang-tri', 'tinh', 'Tỉnh Quảng Trị'),
(08, 'Tuyên Quang', 'tuyen-quang', 'tinh', 'Tỉnh Tuyên Quang'),
(51, 'Quảng Ngãi', 'quang-ngai', 'tinh', 'Tỉnh Quảng Ngãi'),
(01, 'Hà Nội', 'ha-noi', 'thanh-pho', 'Thành phố Hà Nội'),
(10, 'Lào Cai', 'lao-cai', 'tinh', 'Tỉnh Lào Cai'),
(86, 'Vĩnh Long', 'vinh-long', 'tinh', 'Tỉnh Vĩnh Long'),
(68, 'Lâm Đồng', 'lam-dong', 'tinh', 'Tỉnh Lâm Đồng'),
(52, 'Bình Định', 'binh-dinh', 'tinh', 'Tỉnh Bình Định'),
(40, 'Nghệ An', 'nghe-an', 'tinh', 'Tỉnh Nghệ An'),
(91, 'Kiên Giang', 'kien-giang', 'tinh', 'Tỉnh Kiên Giang'),
(02, 'Hà Giang', 'ha-giang', 'tinh', 'Tỉnh Hà Giang'),
(54, 'Phú Yên', 'phu-yen', 'tinh', 'Tỉnh Phú Yên'),
(20, 'Lạng Sơn', 'lang-son', 'tinh', 'Tỉnh Lạng Sơn'),
(48, 'Đà Nẵng', 'da-nang', 'thanh-pho', 'Thành phố Đà Nẵng'),
(14, 'Sơn La', 'son-la', 'tinh', 'Tỉnh Sơn La'),
(72, 'Tây Ninh', 'tay-ninh', 'tinh', 'Tỉnh Tây Ninh'),
(36, 'Nam Định', 'nam-dinh', 'tinh', 'Tỉnh Nam Định'),
(12, 'Lai Châu', 'lai-chau', 'tinh', 'Tỉnh Lai Châu'),
(83, 'Bến Tre', 'ben-tre', 'tinh', 'Tỉnh Bến Tre'),
(56, 'Khánh Hòa', 'khanh-hoa', 'tinh', 'Tỉnh Khánh Hòa'),
(60, 'Bình Thuận', 'binh-thuan', 'tinh', 'Tỉnh Bình Thuận'),
(04, 'Cao Bằng', 'cao-bang', 'tinh', 'Tỉnh Cao Bằng'),
(31, 'Hải Phòng', 'hai-phong', 'thanh-pho', 'Thành phố Hải Phòng'),
(37, 'Ninh Bình', 'ninh-binh', 'tinh', 'Tỉnh Ninh Bình'),
(15, 'Yên Bái', 'yen-bai', 'tinh', 'Tỉnh Yên Bái'),
(64, 'Gia Lai', 'gia-lai', 'tinh', 'Tỉnh Gia Lai'),
(17, 'Hoà Bình', 'hoa-binh', 'tinh', 'Tỉnh Hoà Bình'),
(77, 'Bà Rịa - Vũng Tàu', 'ba-ria-vung-tau', 'tinh', 'Tỉnh Bà Rịa - Vũng Tàu'),
(96, 'Cà Mau', 'ca-mau', 'tinh', 'Tỉnh Cà Mau'),
(74, 'Bình Dương', 'binh-duong', 'tinh', 'Tỉnh Bình Dương'),
(92, 'Cần Thơ', 'can-tho', 'thanh-pho', 'Thành phố Cần Thơ'),
(46, 'Thừa Thiên Huế', 'thua-thien-hue', 'tinh', 'Tỉnh Thừa Thiên Huế'),
(75, 'Đồng Nai', 'dong-nai', 'tinh', 'Tỉnh Đồng Nai'),
(82, 'Tiền Giang', 'tien-giang', 'tinh', 'Tỉnh Tiền Giang'),
(11, 'Điện Biên', 'dien-bien', 'tinh', 'Tỉnh Điện Biên'),
(26, 'Vĩnh Phúc', 'vinh-phuc', 'tinh', 'Tỉnh Vĩnh Phúc'),
(49, 'Quảng Nam', 'quang-nam', 'tinh', 'Tỉnh Quảng Nam'),
(66, 'Đắk Lắk', 'dak-lak', 'tinh', 'Tỉnh Đắk Lắk'),
(19, 'Thái Nguyên', 'thai-nguyen', 'tinh', 'Tỉnh Thái Nguyên'),
(30, 'Hải Dương', 'hai-duong', 'tinh', 'Tỉnh Hải Dương'),
(95, 'Bạc Liêu', 'bac-lieu', 'tinh', 'Tỉnh Bạc Liêu'),
(84, 'Trà Vinh', 'tra-vinh', 'tinh', 'Tỉnh Trà Vinh'),
(34, 'Thái Bình', 'thai-binh', 'tinh', 'Tỉnh Thái Bình'),
(42, 'Hà Tĩnh', 'ha-tinh', 'tinh', 'Tỉnh Hà Tĩnh'),
(58, 'Ninh Thuận', 'ninh-thuan', 'tinh', 'Tỉnh Ninh Thuận'),
(87, 'Đồng Tháp', 'dong-thap', 'tinh', 'Tỉnh Đồng Tháp'),
(80, 'Long An', 'long-an', 'tinh', 'Tỉnh Long An'),
(93, 'Hậu Giang', 'hau-giang', 'tinh', 'Tỉnh Hậu Giang'),
(22, 'Quảng Ninh', 'quang-ninh', 'tinh', 'Tỉnh Quảng Ninh'),
(25, 'Phú Thọ', 'phu-tho', 'tinh', 'Tỉnh Phú Thọ'),
(44, 'Quảng Bình', 'quang-binh', 'tinh', 'Tỉnh Quảng Bình'),
(79, 'Hồ Chí Minh', 'ho-chi-minh', 'thanh-pho', 'Thành phố Hồ Chí Minh'),
(35, 'Hà Nam', 'ha-nam', 'tinh', 'Tỉnh Hà Nam'),
(27, 'Bắc Ninh', 'bac-ninh', 'tinh', 'Tỉnh Bắc Ninh'),
(24, 'Bắc Giang', 'bac-giang', 'tinh', 'Tỉnh Bắc Giang'),
(06, 'Bắc Kạn', 'bac-kan', 'tinh', 'Tỉnh Bắc Kạn');

